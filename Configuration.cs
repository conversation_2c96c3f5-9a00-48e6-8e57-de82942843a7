using Dalamud.Configuration;
using Dalamud.Plugin;
using System;
using System.Numerics;

namespace PvPLinePlugin;

public enum IndicatorType
{
    // Traditional 2D indicators (ImGui-based)
    Lines,
    Outlines,
    Nameplates,
    Icons,
    DirectionalArrows,
    HealthBars,
    Combination,
    ScreenEdgeArrows,
    GradientLines,
    AnimatedPulse,

    // Enhanced 3D indicators (Splatoon-based)
    SplatoonWorldCircles,
    SplatoonWorldMarkers,
    SplatoonVfxOmens,
    SplatoonVfxTethers,
    SplatoonGroundMarkers,
    SplatoonFloatingIcons,
    SplatoonBeams,
    SplatoonCones,
    SplatoonSpheres,
    SplatoonCustomShapes,

    // Hybrid indicators (Both systems)
    HybridLinesAndMarkers,
    HybridIconsAndBeams,
    HybridAdvancedCombo
}

[Serializable]
public class Configuration : IPluginConfiguration
{
    public int Version { get; set; } = 0;
    public bool Enabled { get; set; } = true;

    #region Visual Indicator Settings
    private IndicatorType _indicatorType = IndicatorType.Lines;
    private Vector4 _lineColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
    private float _lineThickness = 2.0f;
    private float _outlineThickness = 3.0f;
    private float _iconSize = 20.0f;
    private float _arrowSize = 15.0f;

    public IndicatorType IndicatorType
    {
        get => _indicatorType;
        set => _indicatorType = value;
    }

    public Vector4 LineColor
    {
        get => _lineColor;
        set => _lineColor = ValidateColor(value);
    }

    public float LineThickness
    {
        get => _lineThickness;
        set => _lineThickness = Math.Clamp(value, 1.0f, 15.0f);
    }

    public bool ColorCodeByRole { get; set; } = true;

    // Alternative indicator settings
    public float OutlineThickness
    {
        get => _outlineThickness;
        set => _outlineThickness = Math.Clamp(value, 1.0f, 15.0f);
    }

    public float IconSize
    {
        get => _iconSize;
        set => _iconSize = Math.Clamp(value, 5.0f, 100.0f);
    }

    public float ArrowSize
    {
        get => _arrowSize;
        set => _arrowSize = Math.Clamp(value, 5.0f, 50.0f);
    }

    public bool ShowDirectionalArrows { get; set; } = true;
    public bool PulseIndicators { get; set; } = false;

    // New 3D and VFX settings
    public bool Use3DWorldSpace { get; set; } = false;
    public bool EnableVfxEffects { get; set; } = false;
    public float VfxOpacity { get; set; } = 0.8f;
    public bool ShowGroundCircles { get; set; } = true;
    public float GroundCircleRadius { get; set; } = 2.0f;
    public bool EnableTethers { get; set; } = false;
    public string TetherVfxPath { get; set; } = "chn_nomal01f";

    // Enhanced 2D settings
    public bool UseGradientLines { get; set; } = false;
    public Vector4 GradientEndColor { get; set; } = new Vector4(1.0f, 1.0f, 0.0f, 1.0f);
    public bool UseDashedLines { get; set; } = false;
    public float DashLength { get; set; } = 5.0f;
    public float GapLength { get; set; } = 3.0f;
    public bool ShowScreenEdgeArrows { get; set; } = true;
    public float ScreenEdgeMargin { get; set; } = 50.0f;
    public bool UseDistanceBasedColors { get; set; } = false;
    public bool UseHealthBasedColors { get; set; } = false;

    // Animation settings
    public float PulseSpeed { get; set; } = 2.0f;
    public float PulseAmount { get; set; } = 0.3f;
    public bool AnimateOnLowHealth { get; set; } = true;
    public bool AnimateOnDefensiveBuff { get; set; } = false;
    #endregion

    #region Distance Settings
    private float _maxDistance = 50.0f;
    private float _playerListMaxDistance = 100.0f;

    public float MaxDistance
    {
        get => _maxDistance;
        set => _maxDistance = Math.Clamp(value, 5.0f, 200.0f);
    }

    public bool ShowDistance { get; set; } = true;
    #endregion

    #region PvP Settings
    public bool OnlyInPvP { get; set; } = true;
    public bool ShowInCombatOnly { get; set; } = false;
    #endregion

    #region Player Information
    public bool ShowPlayerNames { get; set; } = false;
    public bool ShowPlayerJobs { get; set; } = true;
    public bool ShowJobIcons { get; set; } = false;
    public bool ShowStatusEffects { get; set; } = true;
    public bool ShowOnlyImportantStatus { get; set; } = true;
    public bool ShowDefensiveBuffs { get; set; } = true;
    #endregion

    #region Low Health Settings
    public bool ShowLowHealthIndicator { get; set; } = true;
    public float LowHealthThreshold { get; set; } = 25.0f;
    public Vector4 LowHealthLineColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f);
    public float LowHealthLineThickness { get; set; } = 5.0f;
    public bool ShowHealthPercentage { get; set; } = true;
    public bool PulseKillableTargets { get; set; } = true;
    #endregion

    #region Enemy/Ally Detection
    public bool ShowAllies { get; set; } = false;
    public bool ShowEnemies { get; set; } = true;
    public Vector4 AllyLineColor { get; set; } = new Vector4(0.0f, 1.0f, 0.0f, 0.8f);
    public float AllyLineThickness { get; set; } = 2.0f;
    public bool ShowAllyEnemyIndicator { get; set; } = true;
    public bool DifferentColorsForAllies { get; set; } = true;
    #endregion

    #region Splatoon Integration Settings
    public bool UseSplatoonRendering { get; set; } = false;
    public bool SplatoonWorldMarkersEnabled { get; set; } = true;
    public float SplatoonMarkerRadius { get; set; } = 3.0f;
    public float SplatoonMarkerHeight { get; set; } = 2.0f;
    public uint SplatoonMarkerColor { get; set; } = 0xFF0000FF; // Red
    public uint SplatoonAllyMarkerColor { get; set; } = 0xFF00FF00; // Green

    // VFX Settings
    public bool SplatoonVfxEnabled { get; set; } = false;
    public string SplatoonVfxPath { get; set; } = "vfx/common/eff/wp_astro_horoscope01_02t.avfx";
    public float SplatoonVfxScale { get; set; } = 1.0f;
    public float SplatoonVfxDuration { get; set; } = 5.0f;

    // Beam Settings
    public bool SplatoonBeamsEnabled { get; set; } = false;
    public float SplatoonBeamThickness { get; set; } = 0.5f;
    public uint SplatoonBeamColor { get; set; } = 0xFF0000FF;

    // Shape Settings
    public bool SplatoonCustomShapesEnabled { get; set; } = false;
    public float SplatoonShapeScale { get; set; } = 1.0f;
    public bool SplatoonShapesFillInside { get; set; } = false;
    public float SplatoonShapeOpacity { get; set; } = 0.7f;

    // Performance Settings
    public int SplatoonMaxElements { get; set; } = 50;
    public bool SplatoonLimitByDistance { get; set; } = true;
    public float SplatoonMaxRenderDistance { get; set; } = 100.0f;
    #endregion

    #region Player List Settings
    public bool ShowPlayerList { get; set; } = false;
    public bool PlayerListShowAllies { get; set; } = true;
    public bool PlayerListShowEnemies { get; set; } = true;
    public bool PlayerListShowDistance { get; set; } = true;
    public bool PlayerListShowHealth { get; set; } = true;
    public bool PlayerListShowJob { get; set; } = true;
    public bool PlayerListShowStatus { get; set; } = true;
    public bool PlayerListAutoHide { get; set; } = false; // Hide when not in PvP
    public float PlayerListMaxDistance
    {
        get => _playerListMaxDistance;
        set => _playerListMaxDistance = Math.Clamp(value, 10.0f, 300.0f);
    }

    #endregion





    public void Save() => Plugin.PluginInterface.SavePluginConfig(this);

    /// <summary>
    /// Migrate configuration from older versions
    /// </summary>
    public void MigrateConfiguration()
    {
        // Version 0 to 1: Add Splatoon settings
        if (Version == 0)
        {
            // Set default Splatoon settings based on existing preferences
            UseSplatoonRendering = false; // Default to traditional rendering
            SplatoonWorldMarkersEnabled = true;
            SplatoonMarkerRadius = 3.0f;
            SplatoonMarkerHeight = 2.0f;

            // Convert existing line color to Splatoon colors
            var lineColorU32 = LineColor.ToImGuiColor();
            SplatoonMarkerColor = lineColorU32;
            SplatoonAllyMarkerColor = AllyLineColor.ToImGuiColor();

            // Set VFX defaults
            SplatoonVfxEnabled = false;
            SplatoonVfxPath = "vfx/common/eff/wp_astro_horoscope01_02t.avfx";
            SplatoonVfxScale = 1.0f;
            SplatoonVfxDuration = 5.0f;

            // Set beam defaults
            SplatoonBeamsEnabled = false;
            SplatoonBeamThickness = LineThickness * 0.1f; // Scale down for 3D
            SplatoonBeamColor = lineColorU32;

            // Set shape defaults
            SplatoonCustomShapesEnabled = false;
            SplatoonShapeScale = 1.0f;
            SplatoonShapesFillInside = false;
            SplatoonShapeOpacity = 0.7f;

            // Set performance defaults
            SplatoonMaxElements = 50;
            SplatoonLimitByDistance = true;
            SplatoonMaxRenderDistance = MaxDistance * 2.0f; // Larger range for 3D

            Version = 1;
            Save();
        }
    }

    // Validation methods
    private static Vector4 ValidateColor(Vector4 color)
    {
        return new Vector4(
            Math.Clamp(color.X, 0.0f, 1.0f),
            Math.Clamp(color.Y, 0.0f, 1.0f),
            Math.Clamp(color.Z, 0.0f, 1.0f),
            Math.Clamp(color.W, 0.0f, 1.0f)
        );
    }
}
