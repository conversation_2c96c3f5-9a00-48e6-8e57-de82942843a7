# Enhanced FFXIV Indicator System

## Overview

Your PvP Line Plugin has been enhanced with advanced indicator rendering capabilities that provide alternatives to traditional ImGui-based indicators. The new system offers enhanced 2D rendering with 3D-like effects and is designed to be extensible for future Splatoon API integration.

## What's New

### Enhanced Indicator Types

The plugin now supports the following enhanced indicator types:

#### Traditional Indicators (ImGui-based)
- **Lines** - Simple lines pointing to targets
- **Outlines** - Outlined shapes around targets  
- **Nameplates** - Text nameplates above targets
- **Icons** - Icon symbols for targets
- **Directional Arrows** - Arrows pointing toward targets
- **Health Bars** - Health bar displays
- **Combination** - Multiple indicator types combined
- **Screen Edge Arrows** - Arrows at screen edges for off-screen targets
- **Gradient Lines** - Lines with color gradients
- **Animated Pulse** - Pulsing animated indicators

#### Enhanced 3D-Style Indicators (Enhanced 2D Rendering)
- **3D World Circles** - Concentric circles with 3D depth effect
- **3D World Markers** - Diamond-shaped markers with enhanced visuals
- **VFX Omens** - Rotating segment effects like boss omens
- **VFX Tethers** - Animated beam effects with energy particles
- **Ground Markers** - Cross-pattern markers for ground positioning
- **3D Beams** - Gradient beam lines with core highlights
- **Directional Cones** - Cone shapes pointing toward targets
- **3D Spheres** - Multi-layered circles simulating 3D spheres

#### Hybrid Indicators
- **Hybrid: Lines + Markers** - Combines traditional lines with enhanced markers
- **Hybrid: Icons + Beams** - Combines icons with enhanced beams
- **Hybrid: Advanced Combo** - Advanced combination of multiple effects

## How to Use

### Enabling Enhanced Rendering

1. Open the plugin configuration (`/pvplines config`)
2. Go to the **Appearance** tab
3. Check **"Use Enhanced 3D Rendering (Splatoon)"**
4. Select your preferred enhanced indicator type from the dropdown

### Configuration Options

#### Enhanced Rendering Settings
- **Enable World Markers** - Toggle 3D-style world markers
- **Marker Radius** - Size of 3D markers (0.5-10.0)
- **Marker Height** - Height offset for markers (0.0-5.0)
- **Enemy/Ally Marker Colors** - Separate colors for enemies and allies

#### VFX Settings (for VFX Omens/Tethers)
- **Enable VFX Effects** - Toggle VFX-style rendering
- **VFX Path** - Custom VFX path (for future Splatoon integration)
- **VFX Scale** - Scale multiplier for VFX effects
- **VFX Duration** - Duration for VFX effects

#### Beam Settings (for Beam indicators)
- **Enable Beams** - Toggle beam rendering
- **Beam Thickness** - Thickness of beam lines
- **Beam Color** - Color for beam effects

#### Shape Settings (for Custom Shapes)
- **Enable Custom Shapes** - Toggle custom shape rendering
- **Shape Scale** - Scale multiplier for shapes
- **Fill Inside** - Whether to fill shapes with color
- **Shape Opacity** - Transparency of shape fills

#### Performance Settings
- **Max Elements** - Maximum number of elements to render (10-100)
- **Limit by Distance** - Enable distance-based culling
- **Max Render Distance** - Maximum distance for rendering (50-200)

## Technical Implementation

### Architecture

The enhanced indicator system consists of several key components:

1. **EnhancedIndicatorRenderer** - Main rendering class that handles enhanced 2D effects
2. **SplatoonElement** - Data structure for future Splatoon API integration
3. **ColorUtils** - Utility class for color manipulation and effects
4. **ImGuiUtils** - Extended ImGui drawing utilities for enhanced effects

### Rendering Pipeline

1. **World to Screen Conversion** - Converts 3D world positions to 2D screen coordinates
2. **Enhanced 2D Rendering** - Uses advanced ImGui drawing techniques to create 3D-like effects
3. **Animation System** - Time-based animations for pulsing, rotating, and particle effects
4. **Performance Optimization** - Distance culling and element limiting for smooth performance

### Future Splatoon Integration

The system is designed to be easily extensible for true 3D Splatoon API integration:

- **SplatoonElement** class provides the data structure for Splatoon elements
- **Configuration migration** system handles upgrading from 2D to 3D rendering
- **Modular design** allows switching between rendering backends

## Configuration Migration

When upgrading from the previous version, your existing settings will be automatically migrated:

- Line colors are converted to enhanced marker colors
- Line thickness is scaled appropriately for 3D rendering
- Distance settings are expanded for larger 3D render distances
- All existing preferences are preserved

## Performance Considerations

The enhanced rendering system is optimized for performance:

- **Distance Culling** - Only renders indicators within specified range
- **Element Limiting** - Caps maximum number of rendered elements
- **Efficient Drawing** - Uses optimized ImGui drawing calls
- **Frame-based Rendering** - No persistent state between frames

## Troubleshooting

### Common Issues

1. **Enhanced rendering not working**
   - Ensure "Use Enhanced 3D Rendering" is enabled
   - Check that ECommons is properly installed
   - Verify you're in a PvP zone (if "Only in PvP" is enabled)

2. **Performance issues**
   - Reduce "Max Elements" setting
   - Enable "Limit by Distance" 
   - Reduce "Max Render Distance"
   - Switch to simpler indicator types

3. **Visual artifacts**
   - Adjust marker radius and height settings
   - Modify opacity settings for better visibility
   - Try different indicator types

### Debug Information

The plugin logs enhanced rendering errors to the Dalamud log. Check for:
- "Error in enhanced indicator rendering" messages
- ECommons initialization errors
- Configuration migration issues

## Future Enhancements

Planned improvements include:

1. **True Splatoon 3D Integration** - Full 3D world-space rendering
2. **Custom VFX Effects** - Integration with game's VFX system
3. **Advanced Animation System** - More complex animation patterns
4. **Performance Profiling** - Built-in performance monitoring
5. **User-defined Shapes** - Custom indicator shape creation

## Support

For issues or questions about the enhanced indicator system:

1. Check the troubleshooting section above
2. Review the Dalamud log for error messages
3. Test with different indicator types and settings
4. Report issues with specific configuration details
