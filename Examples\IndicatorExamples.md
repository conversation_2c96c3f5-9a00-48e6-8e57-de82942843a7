# Enhanced Indicator Types - Usage Examples

This document provides examples of how to use the new enhanced indicator types in PvPLinePlugin.

## New Indicator Types

### 1. World Circles (3D)
Creates circles around enemies in 3D world space that are visible from any angle.

**Configuration:**
- Enable "3D World Space Rendering" 
- Set "Circle Radius" (0.5-10.0)
- Choose circle color

**Best for:** Clear visual indication of enemy positions that works regardless of camera angle.

### 2. VFX Omens
Uses the game's native VFX system to create boss-style omen effects around enemies.

**Configuration:**
- Enable "VFX Effects"
- Set "VFX Opacity" (0.1-1.0)
- Set "Omen Radius" (1.0-15.0)

**Best for:** Immersive, game-integrated visual effects that feel natural.

### 3. VFX Tethers
Creates beam/tether effects connecting you to enemies using the game's VFX system.

**Configuration:**
- Enable "Tethers"
- Set "Tether VFX Path" (default: "chn_nomal01f")
- Set "Tether Opacity" (0.1-1.0)

**Best for:** Clear visual connections showing which enemies you're tracking.

### 4. Ground Markers
Places markers on the ground at enemy positions, visible through terrain.

**Configuration:**
- Enable "Ground Markers"
- Set "Marker Size" (0.5-5.0)
- Choose marker color
- Enable "Distance-Based Colors" for intensity scaling

**Best for:** Tracking enemy positions even when they're behind obstacles.

### 5. Floating Icons
3D icons that float above enemies showing their job and status.

**Configuration:**
- Set "Icon Size" (10-100)
- Choose icon color
- Enable "Show Job Icons" for role-specific icons

**Best for:** Quick identification of enemy roles and status at a glance.

### 6. Screen Edge Arrows
Shows arrows at screen edges pointing towards off-screen enemies.

**Configuration:**
- Enable "Screen Edge Arrows"
- Set "Arrow Size" (10-50)
- Set "Edge Margin" (20-100) - distance from screen edge
- Choose arrow color

**Best for:** Tracking enemies that are outside your current view.

### 7. Gradient Lines
Lines with smooth color gradients from start to end point.

**Configuration:**
- Set "Start Color" and "End Color"
- Set "Line Thickness" (1-10)
- Enable "Distance-Based Gradient" for dynamic colors

**Best for:** Visually appealing lines that provide additional information through color.

### 8. Animated Pulse
Pulsing animated indicators with customizable speed and intensity.

**Configuration:**
- Set "Pulse Speed" (0.5-5.0)
- Set "Pulse Amount" (0.1-1.0)
- Enable "Animate on Low Health" for conditional animation
- Enable "Health-Based Colors" for dynamic coloring

**Best for:** Drawing attention to important targets or status changes.

## Advanced Features

### Distance-Based Colors
Many indicator types support distance-based coloring where indicators become more intense the closer enemies are to you.

### Health-Based Colors
Indicators can change color based on enemy health percentage:
- Green: High health (>60%)
- Yellow: Medium health (30-60%)
- Red: Low health (<30%)

### Role-Based Colors
Automatic coloring based on enemy job roles:
- Blue: Tank jobs
- Green: Healer jobs
- Red: Melee DPS jobs
- Orange: Ranged Physical DPS jobs
- Purple: Magical DPS jobs

### Animation Conditions
Animations can be triggered by specific conditions:
- Low health enemies
- Enemies with defensive buffs
- Always animate (default)

## Performance Considerations

### 3D Indicators (World Circles, VFX)
- More resource-intensive than 2D indicators
- Use sparingly in large battles
- Consider disabling if experiencing frame rate issues

### 2D Enhanced Indicators
- Gradient lines and animations have minimal performance impact
- Screen edge arrows are very lightweight
- Floating icons with health bars may impact performance with many enemies

## Recommended Configurations

### PvP Frontlines (Large Battles)
- Use Screen Edge Arrows for off-screen tracking
- Enable Distance-Based Colors for priority targeting
- Avoid VFX effects to maintain performance

### PvP Crystalline Conflict (Small Battles)
- Use VFX Omens or World Circles for immersive experience
- Enable Animated Pulse for low health enemies
- Use Floating Icons for role identification

### General PvP
- Gradient Lines with Health-Based Colors
- Screen Edge Arrows for situational awareness
- Ground Markers for tracking through obstacles

## Troubleshooting

### 3D Indicators Not Showing
- Ensure "3D World Space Rendering" is enabled
- Check that Pictomancy service initialized correctly
- Try restarting the plugin

### VFX Effects Not Working
- Verify "VFX Effects" is enabled
- Check VFX path is correct (default: "general01bf" for omens)
- Some VFX may not work in certain zones

### Performance Issues
- Disable 3D indicators and VFX effects
- Reduce animation speed and intensity
- Use simpler indicator types like Lines or Icons

### Colors Not Changing
- Verify color-based features are enabled in configuration
- Check that the indicator type supports the color feature
- Ensure enemies are within detection range
