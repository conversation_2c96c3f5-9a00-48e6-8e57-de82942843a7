using Dalamud.Game.Command;
using Dalamud.IoC;
using Dalamud.Plugin;
using Dalamud.Interface.Windowing;
using Dalamud.Plugin.Services;
using Dalamud.Game.ClientState.Objects.SubKinds;
using PvPLinePlugin.Windows;
using ECommons;
using ECommons.DalamudServices;
using static ECommons.ECommonsMain;

namespace PvPLinePlugin;

public sealed class Plugin : IDalamudPlugin
{
    [PluginService] internal static IDalamudPluginInterface PluginInterface { get; private set; } = null!;
    [PluginService] internal static ITextureProvider TextureProvider { get; private set; } = null!;
    [PluginService] internal static ICommandManager CommandManager { get; private set; } = null!;
    [PluginService] internal static IClientState ClientState { get; private set; } = null!;
    [PluginService] internal static IDataManager DataManager { get; private set; } = null!;
    [PluginService] internal static IPluginLog Log { get; private set; } = null!;
    [PluginService] internal static IObjectTable ObjectTable { get; private set; } = null!;
    [PluginService] internal static IGameGui GameGui { get; private set; } = null!;
    [PluginService] internal static ICondition Condition { get; private set; } = null!;
    [PluginService] internal static IPartyList PartyList { get; private set; } = null!;

    private const string CommandName = "/pvplines";


    public Configuration Configuration { get; init; }
    public readonly WindowSystem WindowSystem = new("PvPLinePlugin");
    
    private ConfigWindow ConfigWindow { get; init; }
    private MainWindow MainWindow { get; init; }
    private PlayerListWindow PlayerListWindow { get; init; }
    private PvPOverlay PvPOverlay { get; init; }

    public Plugin()
    {
        // Initialize ECommons with Splatoon API support
        ECommonsMain.Init(PluginInterface, this, Module.SplatoonAPI);

        Configuration = PluginInterface.GetPluginConfig() as Configuration ?? new Configuration();

        // Migrate configuration if needed
        Configuration.MigrateConfiguration();

        ConfigWindow = new ConfigWindow(this);
        MainWindow = new MainWindow(this);
        PlayerListWindow = new PlayerListWindow(this);
        PvPOverlay = new PvPOverlay(this);

        WindowSystem.AddWindow(ConfigWindow);
        WindowSystem.AddWindow(MainWindow);
        WindowSystem.AddWindow(PlayerListWindow);

        CommandManager.AddHandler(CommandName, new CommandInfo(OnCommand)
        {
            HelpMessage = "Open PvP Line Plugin configuration"
        });



        PluginInterface.UiBuilder.Draw += DrawUI;
        PluginInterface.UiBuilder.OpenConfigUi += ToggleConfigUI;
        PluginInterface.UiBuilder.OpenMainUi += ToggleMainUI;

        Log.Information($"PvP Line Plugin loaded successfully!");
    }

    public void Dispose()
    {
        WindowSystem.RemoveAllWindows();

        ConfigWindow.Dispose();
        MainWindow.Dispose();
        PlayerListWindow.Dispose();
        PvPOverlay.Dispose();

        CommandManager.RemoveHandler(CommandName);

        // Dispose ECommons
        ECommonsMain.Dispose();
    }

    private void OnCommand(string command, string args)
    {
        // Toggle the main UI or config based on args
        if (args.ToLower() == "config")
        {
            ToggleConfigUI();
        }
        else
        {
            ToggleMainUI();
        }
    }



    private void DrawUI() => WindowSystem.Draw();

    public void ToggleConfigUI() => ConfigWindow.Toggle();
    public void ToggleMainUI() => MainWindow.Toggle();
    public void TogglePlayerListUI() => PlayerListWindow.Toggle();
}
