using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;
using PvPLinePlugin.Utils;

namespace PvPLinePlugin.Rendering;

public class EnhancedIndicatorRenderer : IDisposable
{
    private readonly Configuration config;

    public EnhancedIndicatorRenderer(Configuration configuration)
    {
        config = configuration;
    }

    public void Dispose()
    {
        // Enhanced 2D rendering doesn't require cleanup
        GC.SuppressFinalize(this);
    }

    public void RenderIndicators(IPlayerCharacter localPlayer, List<IPlayerCharacter> enemies, List<IPlayerCharacter> allies)
    {
        if (!config.UseSplatoonRendering)
            return;

        try
        {
            // For now, use enhanced 2D rendering until Splatoon integration is fully implemented
            var drawList = ImGui.GetBackgroundDrawList();

            // Render enemy indicators
            if (config.ShowEnemies)
            {
                foreach (var enemy in enemies)
                {
                    RenderEnhanced2DIndicator(drawList, localPlayer, enemy, false);
                }
            }

            // Render ally indicators
            if (config.ShowAllies)
            {
                foreach (var ally in allies)
                {
                    RenderEnhanced2DIndicator(drawList, localPlayer, ally, true);
                }
            }
        }
        catch (Exception ex)
        {
            Plugin.Log.Error($"Error in enhanced indicator rendering: {ex.Message}");
        }
    }

    private void RenderEnhanced2DIndicator(ImDrawListPtr drawList, IPlayerCharacter localPlayer, IPlayerCharacter target, bool isAlly)
    {
        if (target == null || localPlayer == null)
            return;

        var distance = Vector3.Distance(localPlayer.Position, target.Position);
        if (config.SplatoonLimitByDistance && distance > config.SplatoonMaxRenderDistance)
            return;

        // Convert world positions to screen positions
        if (!Plugin.GameGui.WorldToScreen(target.Position, out var targetScreen) ||
            !Plugin.GameGui.WorldToScreen(localPlayer.Position, out var playerScreen))
            return;

        switch (config.IndicatorType)
        {
            case IndicatorType.SplatoonWorldCircles:
                RenderEnhancedWorldCircle(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonWorldMarkers:
                RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonVfxOmens:
                RenderEnhancedVfxOmen(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonVfxTethers:
                RenderEnhancedVfxTether(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonGroundMarkers:
                RenderEnhancedGroundMarker(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonBeams:
                RenderEnhancedBeam(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonCones:
                RenderEnhancedCone(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.SplatoonSpheres:
                RenderEnhancedSphere(drawList, targetScreen, isAlly);
                break;
            case IndicatorType.HybridLinesAndMarkers:
                RenderHybridLinesAndMarkers(drawList, playerScreen, targetScreen, isAlly);
                break;
            case IndicatorType.HybridIconsAndBeams:
                RenderHybridIconsAndBeams(drawList, playerScreen, targetScreen, isAlly);
                break;
        }
    }

    private void RenderEnhancedWorldCircle(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonWorldMarkersEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 20; // Scale for screen space

        if (config.SplatoonShapesFillInside)
        {
            drawList.AddCircleFilled(targetScreen, radius, ColorUtils.WithAlpha(color, 0.3f));
        }

        // Draw multiple concentric circles for enhanced 3D effect
        drawList.AddConcentricCircles(targetScreen, radius, color, 3, 5.0f, 0.2f);
    }

    private void RenderEnhancedWorldMarker(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonWorldMarkersEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var size = config.SplatoonMarkerRadius * 15; // Scale for screen space

        // Draw diamond-shaped marker
        var points = new Vector2[]
        {
            new(targetScreen.X, targetScreen.Y - size),      // Top
            new(targetScreen.X + size, targetScreen.Y),      // Right
            new(targetScreen.X, targetScreen.Y + size),      // Bottom
            new(targetScreen.X - size, targetScreen.Y)       // Left
        };

        // Fill
        drawList.AddConvexPolyFilled(ref points[0], 4, ColorUtils.WithAlpha(color, 0.6f));

        // Border
        for (int i = 0; i < 4; i++)
        {
            drawList.AddLine(points[i], points[(i + 1) % 4], color, 2.0f);
        }
    }

    private void RenderEnhancedVfxOmen(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonVfxEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 25; // Scale for omen effect

        // Draw omen-style rotating segments
        var time = (float)ImGui.GetTime();
        var segments = 8;
        var segmentAngle = (float)(2 * Math.PI / segments);

        for (int i = 0; i < segments; i++)
        {
            var angle = time * 2.0f + i * segmentAngle;
            var innerRadius = radius * 0.7f;
            var outerRadius = radius;

            var innerPoint = targetScreen + new Vector2(
                (float)Math.Cos(angle) * innerRadius,
                (float)Math.Sin(angle) * innerRadius
            );
            var outerPoint = targetScreen + new Vector2(
                (float)Math.Cos(angle) * outerRadius,
                (float)Math.Sin(angle) * outerRadius
            );

            var alpha = (float)(0.3f + 0.7f * Math.Sin(time * 3.0f + i * 0.5f));
            drawList.AddLine(innerPoint, outerPoint, ColorUtils.WithAlpha(color, alpha), 3.0f);
        }
    }

    private void RenderEnhancedVfxTether(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonVfxEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var thickness = config.SplatoonBeamThickness * 10; // Scale for screen space

        // Draw animated tether with pulsing effect
        var time = (float)ImGui.GetTime();
        var pulse = (float)(0.7f + 0.3f * Math.Sin(time * 4.0f));

        // Main tether line
        drawList.AddLine(playerScreen, targetScreen, ColorUtils.WithAlpha(color, pulse), thickness);

        // Add energy particles along the line
        var direction = Vector2.Normalize(targetScreen - playerScreen);
        var distance = Vector2.Distance(playerScreen, targetScreen);
        var particleCount = (int)(distance / 20);

        for (int i = 0; i < particleCount; i++)
        {
            var t = (float)i / particleCount;
            var particlePos = Vector2.Lerp(playerScreen, targetScreen, t);
            var particleOffset = time * 100.0f + i * 20.0f;
            particlePos += direction * (particleOffset % 40.0f - 20.0f);

            drawList.AddCircleFilled(particlePos, 2.0f, ColorUtils.WithAlpha(color, pulse * 0.8f));
        }
    }

    private void RenderEnhancedGroundMarker(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 15; // Scale for screen space

        // Draw ground marker with cross pattern
        drawList.AddCircleFilled(targetScreen, radius, ColorUtils.WithAlpha(color, 0.4f));
        drawList.AddCircle(targetScreen, radius, color, 0, 3.0f);

        // Add cross in the center
        var crossSize = radius * 0.6f;
        drawList.AddLine(
            new Vector2(targetScreen.X - crossSize, targetScreen.Y),
            new Vector2(targetScreen.X + crossSize, targetScreen.Y),
            color, 3.0f);
        drawList.AddLine(
            new Vector2(targetScreen.X, targetScreen.Y - crossSize),
            new Vector2(targetScreen.X, targetScreen.Y + crossSize),
            color, 3.0f);
    }

    private void RenderEnhancedBeam(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        if (!config.SplatoonBeamsEnabled)
            return;

        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonBeamColor;
        var thickness = config.SplatoonBeamThickness * 8; // Scale for screen space

        // Draw main beam with gradient effect
        drawList.AddGradientLine(playerScreen, targetScreen,
            ColorUtils.WithAlpha(color, 0.8f),
            ColorUtils.WithAlpha(color, 0.3f),
            thickness);

        // Add core beam
        drawList.AddLine(playerScreen, targetScreen, color, thickness * 0.3f);
    }

    private void RenderEnhancedCone(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var direction = Vector2.Normalize(targetScreen - playerScreen);
        var distance = Vector2.Distance(playerScreen, targetScreen);
        var coneWidth = Math.Min(distance * 0.3f, config.SplatoonMarkerRadius * 20);

        // Calculate cone points
        var perpendicular = new Vector2(-direction.Y, direction.X);
        var coneBase1 = targetScreen + perpendicular * coneWidth;
        var coneBase2 = targetScreen - perpendicular * coneWidth;

        // Draw cone triangle
        var points = new Vector2[] { playerScreen, coneBase1, coneBase2 };
        drawList.AddConvexPolyFilled(ref points[0], 3, ColorUtils.WithAlpha(color, 0.3f));

        // Draw cone outline
        drawList.AddLine(playerScreen, coneBase1, color, 2.0f);
        drawList.AddLine(playerScreen, coneBase2, color, 2.0f);
        drawList.AddLine(coneBase1, coneBase2, color, 2.0f);
    }

    private void RenderEnhancedSphere(ImDrawListPtr drawList, Vector2 targetScreen, bool isAlly)
    {
        var color = isAlly ? config.SplatoonAllyMarkerColor : config.SplatoonMarkerColor;
        var radius = config.SplatoonMarkerRadius * 18; // Scale for screen space

        if (config.SplatoonShapesFillInside)
        {
            drawList.AddCircleFilled(targetScreen, radius, ColorUtils.WithAlpha(color, 0.4f));
        }

        // Draw multiple circles to simulate 3D sphere
        for (int i = 0; i < 3; i++)
        {
            var layerRadius = radius * (1.0f - i * 0.2f);
            var alpha = 1.0f - i * 0.3f;
            drawList.AddCircle(targetScreen, layerRadius, ColorUtils.WithAlpha(color, alpha), 0, 2.0f);
        }
    }

    private void RenderHybridLinesAndMarkers(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        // Render enhanced world marker
        RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);

        // Add traditional line
        var color = isAlly ? config.AllyLineColor.ToImGuiColor() : config.LineColor.ToImGuiColor();
        drawList.AddLine(playerScreen, targetScreen, color, config.LineThickness);
    }

    private void RenderHybridIconsAndBeams(ImDrawListPtr drawList, Vector2 playerScreen, Vector2 targetScreen, bool isAlly)
    {
        // Render enhanced beam
        RenderEnhancedBeam(drawList, playerScreen, targetScreen, isAlly);

        // Add floating icon marker
        RenderEnhancedWorldMarker(drawList, targetScreen, isAlly);
    }

    // Enhanced 2D rendering doesn't need element pooling like Splatoon
    // All rendering is done directly to ImGui draw list each frame
}
