using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public enum ThreatLevel
{
    Disabled,       // Crowd controlled, can't act
    Vulnerable,     // Vulnerable to damage, easy target
    Normal,         // Standard threat level
    Defensive,      // Has defensive buffs, harder to kill
    ModerateThreat, // Has offensive buffs, moderate danger
    HighThreat      // In burst mode or limit break, very dangerous
}

public static class StatusEffectHelper
{
    // Most important PvP status effects - only the critical ones
    private static readonly Dictionary<uint, StatusInfo> PvPStatusEffects = new()
    {
        // === CROWD CONTROL (Highest Priority) ===
        { 2, new("Sleep", new(0.6f, 0.0f, 1.0f, 1.0f), true) },              // Sleep
        { 3, new("Stun", new(1.0f, 0.8f, 0.0f, 1.0f), true) },               // Stun
        { 6, new("Silence", new(0.8f, 0.0f, 0.8f, 1.0f), true) },            // Silence
        { 7, new("Bind", new(0.7f, 0.4f, 0.0f, 1.0f), true) },               // Bind
        { 9, new("Heavy", new(0.5f, 0.5f, 0.5f, 1.0f), true) },              // Heavy
        { 17, new("Paralysis", new(1.0f, 0.7f, 0.0f, 1.0f), true) },         // Paralysis

        // === VULNERABILITY (High Priority) ===
        { 638, new("Vulnerability Up", new(1.0f, 0.3f, 0.0f, 1.0f), true) }, // Vulnerability Up - easy target

        // === DEFENSIVE BUFFS (Important to Know) ===
        { 1362, new("Guard", new(0.0f, 0.0f, 1.0f, 1.0f), false) },          // Guard - 90% damage reduction
        { 3054, new("Recuperate", new(0.0f, 1.0f, 0.5f, 1.0f), false) },     // Recuperate - healing

        // === MOVEMENT ===
        { 1418, new("Sprint", new(1.0f, 1.0f, 1.0f, 1.0f), false) },         // Sprint - mobility

        // === HEALING ===
        { 158, new("Regen", new(0.0f, 1.0f, 0.0f, 1.0f), false) },           // Regen - healing over time
    };

    // Pre-computed sets for faster lookups - only the most important
    private static readonly HashSet<uint> VulnerabilityStatusIds = [638];
    private static readonly HashSet<uint> CrowdControlStatusIds = [2, 3, 6, 7, 9, 17];
    private static readonly HashSet<uint> DefensiveBuffIds = [1362, 3054];
    private static readonly HashSet<uint> HealingEffectIds = [158];

    public static List<StatusInfo> GetImportantStatusEffects(IPlayerCharacter player, bool onlyNegative = false)
    {
        if (player.StatusList == null) return [];

        return [.. player.StatusList
            .Where(status => PvPStatusEffects.TryGetValue(status.StatusId, out var statusInfo) &&
                           (!onlyNegative || statusInfo.IsNegative))
            .Select(status => new StatusInfo(
                PvPStatusEffects[status.StatusId].Name,
                PvPStatusEffects[status.StatusId].Color,
                PvPStatusEffects[status.StatusId].IsNegative,
                status.RemainingTime))];
    }

    public static bool HasImportantStatusEffects(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => PvPStatusEffects.ContainsKey(status.StatusId)) ?? false;
    }

    // === VULNERABILITY DETECTION ===
    public static bool IsVulnerable(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => VulnerabilityStatusIds.Contains(status.StatusId)) ?? false;
    }

    // === CROWD CONTROL DETECTION ===
    public static bool IsCrowdControlled(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => CrowdControlStatusIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsStunned(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1343) ?? false;
    }

    public static bool IsSilenced(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 6) ?? false;
    }

    public static bool IsBound(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId is 7 or 1345) ?? false;
    }

    // === DEFENSIVE STATE DETECTION ===
    public static bool HasDefensiveBuff(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => DefensiveBuffIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsGuarding(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1362) ?? false;
    }

    public static bool IsRecuperating(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 3054) ?? false;
    }



    // === HEALING STATE DETECTION ===
    public static bool HasHealingEffect(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => HealingEffectIds.Contains(status.StatusId)) ?? false;
    }

    // === MOVEMENT STATE DETECTION ===
    public static bool IsSprinting(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1418) ?? false;
    }

    public static bool IsStealthed(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1368) ?? false;
    }

    // === SIMPLE THREAT ASSESSMENT ===
    public static ThreatLevel GetThreatLevel(IPlayerCharacter player)
    {
        if (IsCrowdControlled(player)) return ThreatLevel.Disabled;
        if (IsGuarding(player)) return ThreatLevel.Defensive;
        if (IsVulnerable(player)) return ThreatLevel.Vulnerable;
        return ThreatLevel.Normal;
    }

    // === SIMPLE TARGET ASSESSMENT ===
    public static bool IsHighPriorityTarget(IPlayerCharacter player)
    {
        return IsVulnerable(player) || IsCrowdControlled(player);
    }

    public static bool IsDangerousEnemy(IPlayerCharacter player)
    {
        return IsSprinting(player) && !IsCrowdControlled(player);
    }


}

public class StatusInfo(string name, Vector4 color, bool isNegative, float? remainingTime = null)
{
    public string Name { get; } = name;
    public Vector4 Color { get; } = color;
    public bool IsNegative { get; } = isNegative;
    public float? RemainingTime { get; } = remainingTime;
}
