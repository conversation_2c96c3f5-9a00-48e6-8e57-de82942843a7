using System;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin.Utils
{
    public static class ColorUtils
    {
        /// <summary>
        /// Converts Vector4 color to ImGui uint color
        /// </summary>
        public static uint ToImGuiColor(this Vector4 color)
        {
            return ImGui.ColorConvertFloat4ToU32(color);
        }

        /// <summary>
        /// Converts ImGui uint color to Vector4
        /// </summary>
        public static Vector4 ToVector4(this uint color)
        {
            return new Vector4(
                (color & 0xFF) / 255.0f,
                ((color >> 8) & 0xFF) / 255.0f,
                ((color >> 16) & 0xFF) / 255.0f,
                ((color >> 24) & 0xFF) / 255.0f
            );
        }

        /// <summary>
        /// Creates a color with modified alpha
        /// </summary>
        public static uint WithAlpha(this uint color, float alpha)
        {
            alpha = Math.Clamp(alpha, 0.0f, 1.0f);
            var newAlpha = (byte)(alpha * 255);
            return (color & 0x00FFFFFF) | ((uint)newAlpha << 24);
        }

        /// <summary>
        /// Creates a color with modified alpha
        /// </summary>
        public static Vector4 WithAlpha(this Vector4 color, float alpha)
        {
            return new Vector4(color.X, color.Y, color.Z, Math.Clamp(alpha, 0.0f, 1.0f));
        }

        /// <summary>
        /// Interpolates between two colors
        /// </summary>
        public static Vector4 Lerp(Vector4 colorA, Vector4 colorB, float t)
        {
            t = Math.Clamp(t, 0.0f, 1.0f);
            return Vector4.Lerp(colorA, colorB, t);
        }

        /// <summary>
        /// Gets a color based on job role
        /// </summary>
        public static Vector4 GetRoleColor(uint jobId)
        {
            // Tank jobs (1-3, 19, 21, 32, 37)
            if (jobId is 1 or 3 or 19 or 21 or 32 or 37)
                return new Vector4(0.0f, 0.6f, 1.0f, 1.0f); // Blue

            // Healer jobs (6, 24, 28, 33, 40)
            if (jobId is 6 or 24 or 28 or 33 or 40)
                return new Vector4(0.0f, 1.0f, 0.3f, 1.0f); // Green

            // Melee DPS jobs (2, 4, 20, 22, 29, 30, 34, 39)
            if (jobId is 2 or 4 or 20 or 22 or 29 or 30 or 34 or 39)
                return new Vector4(1.0f, 0.3f, 0.3f, 1.0f); // Red

            // Ranged Physical DPS jobs (5, 23, 31)
            if (jobId is 5 or 23 or 31)
                return new Vector4(1.0f, 0.8f, 0.0f, 1.0f); // Orange

            // Magical DPS jobs (7, 25, 26, 27, 35, 36, 38, 41, 42)
            if (jobId is 7 or 25 or 26 or 27 or 35 or 36 or 38 or 41 or 42)
                return new Vector4(0.8f, 0.0f, 1.0f, 1.0f); // Purple

            // Default color for unknown jobs
            return new Vector4(0.7f, 0.7f, 0.7f, 1.0f); // Gray
        }

        /// <summary>
        /// Gets a color based on distance (closer = more intense)
        /// </summary>
        public static Vector4 GetDistanceColor(float distance, float maxDistance, Vector4 baseColor)
        {
            var intensity = 1.0f - Math.Clamp(distance / maxDistance, 0.0f, 1.0f);
            return Vector4.Lerp(baseColor.WithAlpha(0.3f), baseColor, intensity);
        }

        /// <summary>
        /// Gets a color based on health percentage
        /// </summary>
        public static Vector4 GetHealthColor(float healthPercent)
        {
            healthPercent = Math.Clamp(healthPercent, 0.0f, 1.0f);

            if (healthPercent > 0.6f)
                return Vector4.Lerp(new Vector4(1.0f, 1.0f, 0.0f, 1.0f), new Vector4(0.0f, 1.0f, 0.0f, 1.0f), (healthPercent - 0.6f) / 0.4f);
            else if (healthPercent > 0.3f)
                return Vector4.Lerp(new Vector4(1.0f, 0.5f, 0.0f, 1.0f), new Vector4(1.0f, 1.0f, 0.0f, 1.0f), (healthPercent - 0.3f) / 0.3f);
            else
                return Vector4.Lerp(new Vector4(1.0f, 0.0f, 0.0f, 1.0f), new Vector4(1.0f, 0.5f, 0.0f, 1.0f), healthPercent / 0.3f);
        }

        /// <summary>
        /// Common color constants
        /// </summary>
        public static class Colors
        {
            public static readonly Vector4 Red = new(1.0f, 0.0f, 0.0f, 1.0f);
            public static readonly Vector4 Green = new(0.0f, 1.0f, 0.0f, 1.0f);
            public static readonly Vector4 Blue = new(0.0f, 0.0f, 1.0f, 1.0f);
            public static readonly Vector4 Yellow = new(1.0f, 1.0f, 0.0f, 1.0f);
            public static readonly Vector4 Orange = new(1.0f, 0.5f, 0.0f, 1.0f);
            public static readonly Vector4 Purple = new(0.8f, 0.0f, 1.0f, 1.0f);
            public static readonly Vector4 Cyan = new(0.0f, 1.0f, 1.0f, 1.0f);
            public static readonly Vector4 White = new(1.0f, 1.0f, 1.0f, 1.0f);
            public static readonly Vector4 Black = new(0.0f, 0.0f, 0.0f, 1.0f);
            public static readonly Vector4 Gray = new(0.5f, 0.5f, 0.5f, 1.0f);
            public static readonly Vector4 LightGray = new(0.7f, 0.7f, 0.7f, 1.0f);
            public static readonly Vector4 DarkGray = new(0.3f, 0.3f, 0.3f, 1.0f);
            
            // Transparent variants
            public static readonly Vector4 RedTransparent = new(1.0f, 0.0f, 0.0f, 0.5f);
            public static readonly Vector4 GreenTransparent = new(0.0f, 1.0f, 0.0f, 0.5f);
            public static readonly Vector4 BlueTransparent = new(0.0f, 0.0f, 1.0f, 0.5f);
            public static readonly Vector4 YellowTransparent = new(1.0f, 1.0f, 0.0f, 0.5f);
        }
    }
}
