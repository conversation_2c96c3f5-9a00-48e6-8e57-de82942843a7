using System;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin.Utils
{
    public static class ColorUtils
    {
        /// <summary>
        /// Converts Vector4 color to ImGui uint color
        /// </summary>
        public static uint ToImGuiColor(this Vector4 color)
        {
            return ImGui.ColorConvertFloat4ToU32(color);
        }

        /// <summary>
        /// Converts ImGui uint color to Vector4
        /// </summary>
        public static Vector4 ToVector4(this uint color)
        {
            return new Vector4(
                ((color >> 0) & 0xFF) / 255f,
                ((color >> 8) & 0xFF) / 255f,
                ((color >> 16) & 0xFF) / 255f,
                ((color >> 24) & 0xFF) / 255f
            );
        }

        /// <summary>
        /// Creates a color with modified alpha
        /// </summary>
        public static uint WithAlpha(uint color, float alpha)
        {
            var a = (byte)(Math.Clamp(alpha, 0f, 1f) * 255);
            return (color & 0x00FFFFFF) | ((uint)a << 24);
        }

        /// <summary>
        /// Creates a Vector4 color with modified alpha
        /// </summary>
        public static Vector4 WithAlpha(Vector4 color, float alpha)
        {
            return new Vector4(color.X, color.Y, color.Z, Math.Clamp(alpha, 0f, 1f));
        }

        /// <summary>
        /// Gets a color based on health percentage
        /// </summary>
        public static Vector4 GetHealthColor(float healthPercent)
        {
            healthPercent = Math.Clamp(healthPercent, 0f, 1f);
            
            if (healthPercent > 0.6f)
                return new Vector4(0f, 1f, 0f, 1f); // Green
            else if (healthPercent > 0.3f)
                return new Vector4(1f, 1f, 0f, 1f); // Yellow
            else
                return new Vector4(1f, 0f, 0f, 1f); // Red
        }

        /// <summary>
        /// Gets a color based on distance (closer = more intense)
        /// </summary>
        public static Vector4 GetDistanceColor(float distance, float maxDistance, Vector4 baseColor)
        {
            var intensity = 1f - Math.Clamp(distance / maxDistance, 0f, 1f);
            return new Vector4(
                baseColor.X * intensity,
                baseColor.Y * intensity,
                baseColor.Z * intensity,
                baseColor.W
            );
        }

        /// <summary>
        /// Gets role color as Vector4
        /// </summary>
        public static Vector4 GetRoleColor(uint roleId)
        {
            return roleId switch
            {
                1 => new Vector4(0.0f, 0.4f, 1.0f, 1.0f),           // Tank - Blue
                2 => new Vector4(0.0f, 0.8f, 0.0f, 1.0f),           // Healer - Green
                3 => new Vector4(1.0f, 0.0f, 0.0f, 1.0f),           // Melee DPS - Red
                4 => new Vector4(1.0f, 0.5f, 0.0f, 1.0f),           // Physical Ranged - Orange
                5 => new Vector4(0.8f, 0.0f, 0.8f, 1.0f),           // Magical Ranged - Purple
                _ => new Vector4(0.7f, 0.7f, 0.7f, 1.0f)            // Unknown - Gray
            };
        }

        /// <summary>
        /// Lerps between two colors
        /// </summary>
        public static Vector4 LerpColor(Vector4 color1, Vector4 color2, float t)
        {
            t = Math.Clamp(t, 0f, 1f);
            return new Vector4(
                color1.X + (color2.X - color1.X) * t,
                color1.Y + (color2.Y - color1.Y) * t,
                color1.Z + (color2.Z - color1.Z) * t,
                color1.W + (color2.W - color1.W) * t
            );
        }

        /// <summary>
        /// Lerps between two uint colors
        /// </summary>
        public static uint LerpColor(uint color1, uint color2, float t)
        {
            var c1 = ToVector4(color1);
            var c2 = ToVector4(color2);
            return LerpColor(c1, c2, t).ToImGuiColor();
        }

        /// <summary>
        /// Common colors as Vector4
        /// </summary>
        public static class Colors
        {
            public static readonly Vector4 White = new(1f, 1f, 1f, 1f);
            public static readonly Vector4 Black = new(0f, 0f, 0f, 1f);
            public static readonly Vector4 Red = new(1f, 0f, 0f, 1f);
            public static readonly Vector4 Green = new(0f, 1f, 0f, 1f);
            public static readonly Vector4 Blue = new(0f, 0f, 1f, 1f);
            public static readonly Vector4 Yellow = new(1f, 1f, 0f, 1f);
            public static readonly Vector4 Cyan = new(0f, 1f, 1f, 1f);
            public static readonly Vector4 Magenta = new(1f, 0f, 1f, 1f);
            public static readonly Vector4 Orange = new(1f, 0.5f, 0f, 1f);
            public static readonly Vector4 Purple = new(0.8f, 0f, 0.8f, 1f);
            public static readonly Vector4 Gray = new(0.5f, 0.5f, 0.5f, 1f);
            public static readonly Vector4 DarkGray = new(0.3f, 0.3f, 0.3f, 1f);
            public static readonly Vector4 LightGray = new(0.8f, 0.8f, 0.8f, 1f);
            public static readonly Vector4 Transparent = new(0f, 0f, 0f, 0f);
        }

        /// <summary>
        /// Converts HSV to RGB
        /// </summary>
        public static Vector4 HSVtoRGB(float h, float s, float v, float a = 1f)
        {
            h = h % 360f;
            s = Math.Clamp(s, 0f, 1f);
            v = Math.Clamp(v, 0f, 1f);
            a = Math.Clamp(a, 0f, 1f);

            var c = v * s;
            var x = c * (1f - Math.Abs((h / 60f) % 2f - 1f));
            var m = v - c;

            float r, g, b;
            if (h < 60f)
            {
                r = c; g = x; b = 0f;
            }
            else if (h < 120f)
            {
                r = x; g = c; b = 0f;
            }
            else if (h < 180f)
            {
                r = 0f; g = c; b = x;
            }
            else if (h < 240f)
            {
                r = 0f; g = x; b = c;
            }
            else if (h < 300f)
            {
                r = x; g = 0f; b = c;
            }
            else
            {
                r = c; g = 0f; b = x;
            }

            return new Vector4(r + m, g + m, b + m, a);
        }

        /// <summary>
        /// Creates a rainbow color based on time
        /// </summary>
        public static Vector4 GetRainbowColor(float time, float speed = 1f)
        {
            var hue = (time * speed * 60f) % 360f;
            return HSVtoRGB(hue, 1f, 1f);
        }

        /// <summary>
        /// Gets a color that contrasts well with the given background color
        /// </summary>
        public static Vector4 GetContrastColor(Vector4 backgroundColor)
        {
            var luminance = 0.299f * backgroundColor.X + 0.587f * backgroundColor.Y + 0.114f * backgroundColor.Z;
            return luminance > 0.5f ? Colors.Black : Colors.White;
        }

        /// <summary>
        /// Darkens a color by the specified amount
        /// </summary>
        public static Vector4 Darken(Vector4 color, float amount)
        {
            amount = Math.Clamp(amount, 0f, 1f);
            return new Vector4(
                color.X * (1f - amount),
                color.Y * (1f - amount),
                color.Z * (1f - amount),
                color.W
            );
        }

        /// <summary>
        /// Lightens a color by the specified amount
        /// </summary>
        public static Vector4 Lighten(Vector4 color, float amount)
        {
            amount = Math.Clamp(amount, 0f, 1f);
            return new Vector4(
                color.X + (1f - color.X) * amount,
                color.Y + (1f - color.Y) * amount,
                color.Z + (1f - color.Z) * amount,
                color.W
            );
        }
    }
}
