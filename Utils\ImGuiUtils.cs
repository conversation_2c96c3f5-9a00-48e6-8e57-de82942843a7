using System;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin.Utils
{
    public static class ImGuiUtils
    {
        /// <summary>
        /// Draws a gradient line between two points
        /// </summary>
        public static void AddGradientLine(this ImDrawListPtr drawList, Vector2 start, Vector2 end, 
            uint startColor, uint endColor, float thickness = 1.0f)
        {
            var distance = Vector2.Distance(start, end);
            var segments = Math.Max(2, (int)(distance / 10)); // One segment per 10 pixels
            
            for (int i = 0; i < segments; i++)
            {
                var t1 = (float)i / segments;
                var t2 = (float)(i + 1) / segments;
                
                var p1 = Vector2.Lerp(start, end, t1);
                var p2 = Vector2.Lerp(start, end, t2);
                var color = ColorUtils.LerpColor(startColor, endColor, t1);
                
                drawList.AddLine(p1, p2, color, thickness);
            }
        }

        /// <summary>
        /// Draws a dashed line between two points
        /// </summary>
        public static void AddDashedLine(this ImDrawListPtr drawList, Vector2 start, Vector2 end, 
            uint color, float thickness = 1.0f, float dashLength = 5.0f, float gapLength = 3.0f)
        {
            var direction = Vector2.Normalize(end - start);
            var totalLength = Vector2.Distance(start, end);
            var segmentLength = dashLength + gapLength;
            var segments = (int)(totalLength / segmentLength);
            
            for (int i = 0; i <= segments; i++)
            {
                var segmentStart = start + direction * (i * segmentLength);
                var segmentEnd = start + direction * Math.Min((i * segmentLength) + dashLength, totalLength);
                
                if (Vector2.Distance(segmentStart, start) < totalLength)
                {
                    drawList.AddLine(segmentStart, segmentEnd, color, thickness);
                }
            }
        }

        /// <summary>
        /// Draws an arrow pointing from start to end
        /// </summary>
        public static void AddArrow(this ImDrawListPtr drawList, Vector2 start, Vector2 end, 
            uint color, float thickness = 2.0f, float arrowHeadSize = 10.0f)
        {
            // Draw the line
            drawList.AddLine(start, end, color, thickness);
            
            // Calculate arrow head
            var direction = Vector2.Normalize(end - start);
            var perpendicular = new Vector2(-direction.Y, direction.X);
            
            var arrowHead1 = end - direction * arrowHeadSize + perpendicular * (arrowHeadSize * 0.5f);
            var arrowHead2 = end - direction * arrowHeadSize - perpendicular * (arrowHeadSize * 0.5f);
            
            // Draw arrow head
            drawList.AddLine(end, arrowHead1, color, thickness);
            drawList.AddLine(end, arrowHead2, color, thickness);
        }

        /// <summary>
        /// Draws a pulsing circle with animated radius
        /// </summary>
        public static void AddPulsingCircle(this ImDrawListPtr drawList, Vector2 center, float baseRadius, 
            uint color, float pulseSpeed = 2.0f, float pulseAmount = 0.3f, float thickness = 1.0f)
        {
            var time = (float)ImGui.GetTime();
            var pulse = (float)(Math.Sin(time * pulseSpeed) * pulseAmount + 1.0f);
            var radius = baseRadius * pulse;
            
            drawList.AddCircle(center, radius, color, 0, thickness);
        }

        /// <summary>
        /// Draws a pulsing filled circle with animated radius and alpha
        /// </summary>
        public static void AddPulsingCircleFilled(this ImDrawListPtr drawList, Vector2 center, float baseRadius, 
            uint baseColor, float pulseSpeed = 2.0f, float pulseAmount = 0.3f)
        {
            var time = (float)ImGui.GetTime();
            var pulse = (float)(Math.Sin(time * pulseSpeed) * pulseAmount + 1.0f);
            var radius = baseRadius * pulse;
            
            // Animate alpha based on pulse
            var alpha = (byte)(255 * (1.0f - pulseAmount + pulseAmount * pulse));
            var color = (baseColor & 0x00FFFFFF) | ((uint)alpha << 24);
            
            drawList.AddCircleFilled(center, radius, color);
        }

        /// <summary>
        /// Draws multiple concentric circles for a 3D effect
        /// </summary>
        public static void AddConcentricCircles(this ImDrawListPtr drawList, Vector2 center, float baseRadius, 
            uint color, int circleCount = 3, float spacing = 5.0f, float alphaDecay = 0.2f)
        {
            for (int i = 0; i < circleCount; i++)
            {
                var radius = baseRadius + (i * spacing);
                var alpha = 1.0f - (i * alphaDecay);
                var circleColor = ColorUtils.WithAlpha(color, alpha);
                drawList.AddCircle(center, radius, circleColor, 0, 2.0f);
            }
        }

        /// <summary>
        /// Draws a directional arrow that points towards a target when off-screen
        /// </summary>
        public static void AddScreenEdgeArrow(this ImDrawListPtr drawList, Vector2 screenCenter, Vector2 targetDirection, 
            uint color, float arrowSize = 15.0f, float distanceFromEdge = 50.0f)
        {
            var screenSize = ImGui.GetIO().DisplaySize;
            var direction = Vector2.Normalize(targetDirection);
            
            // Calculate intersection with screen edges
            var edgePoint = CalculateScreenEdgeIntersection(screenCenter, direction, screenSize, distanceFromEdge);
            
            // Draw arrow pointing towards target
            var arrowStart = edgePoint - direction * arrowSize;
            AddArrow(drawList, arrowStart, edgePoint, color, 2.0f, arrowSize * 0.7f);
        }

        /// <summary>
        /// Calculates where a direction vector intersects with screen edges
        /// </summary>
        private static Vector2 CalculateScreenEdgeIntersection(Vector2 center, Vector2 direction, Vector2 screenSize, float margin)
        {
            var bounds = new Vector2(screenSize.X - margin * 2, screenSize.Y - margin * 2);
            var halfBounds = bounds * 0.5f;
            
            // Calculate intersection with each edge
            var t = float.MaxValue;
            
            // Right edge
            if (direction.X > 0)
                t = Math.Min(t, halfBounds.X / direction.X);
            // Left edge
            else if (direction.X < 0)
                t = Math.Min(t, -halfBounds.X / direction.X);
            
            // Bottom edge
            if (direction.Y > 0)
                t = Math.Min(t, halfBounds.Y / direction.Y);
            // Top edge
            else if (direction.Y < 0)
                t = Math.Min(t, -halfBounds.Y / direction.Y);
            
            var intersection = center + direction * t;
            
            // Clamp to screen bounds with margin
            intersection.X = Math.Clamp(intersection.X, margin, screenSize.X - margin);
            intersection.Y = Math.Clamp(intersection.Y, margin, screenSize.Y - margin);
            
            return intersection;
        }

        /// <summary>
        /// Draws a health bar
        /// </summary>
        public static void AddHealthBar(this ImDrawListPtr drawList, Vector2 position, Vector2 size, 
            float healthPercent, uint backgroundColor, uint healthColor, uint borderColor)
        {
            healthPercent = Math.Clamp(healthPercent, 0f, 1f);
            
            // Background
            drawList.AddRectFilled(position, position + size, backgroundColor);
            
            // Health fill
            var healthWidth = size.X * healthPercent;
            drawList.AddRectFilled(position, new Vector2(position.X + healthWidth, position.Y + size.Y), healthColor);
            
            // Border
            drawList.AddRect(position, position + size, borderColor, 0f, ImDrawFlags.None, 1f);
        }

        /// <summary>
        /// Draws text with an outline
        /// </summary>
        public static void AddTextWithOutline(this ImDrawListPtr drawList, Vector2 position, uint textColor, 
            uint outlineColor, string text, float outlineThickness = 1.0f)
        {
            // Draw outline by drawing text in 8 directions
            for (int x = -1; x <= 1; x++)
            {
                for (int y = -1; y <= 1; y++)
                {
                    if (x == 0 && y == 0) continue;
                    
                    var outlinePos = new Vector2(position.X + x * outlineThickness, position.Y + y * outlineThickness);
                    drawList.AddText(outlinePos, outlineColor, text);
                }
            }
            
            // Draw main text
            drawList.AddText(position, textColor, text);
        }

        /// <summary>
        /// Draws a progress circle (like a cooldown indicator)
        /// </summary>
        public static void AddProgressCircle(this ImDrawListPtr drawList, Vector2 center, float radius, 
            float progress, uint backgroundColor, uint progressColor, float thickness = 2.0f)
        {
            progress = Math.Clamp(progress, 0f, 1f);
            
            // Background circle
            drawList.AddCircle(center, radius, backgroundColor, 0, thickness);
            
            // Progress arc
            if (progress > 0f)
            {
                var startAngle = -MathF.PI / 2f; // Start at top
                var endAngle = startAngle + (progress * 2f * MathF.PI);
                
                // Draw arc by drawing multiple line segments
                var segments = Math.Max(3, (int)(progress * 32));
                var angleStep = (endAngle - startAngle) / segments;
                
                for (int i = 0; i < segments; i++)
                {
                    var angle1 = startAngle + i * angleStep;
                    var angle2 = startAngle + (i + 1) * angleStep;
                    
                    var p1 = center + new Vector2(MathF.Cos(angle1), MathF.Sin(angle1)) * radius;
                    var p2 = center + new Vector2(MathF.Cos(angle2), MathF.Sin(angle2)) * radius;
                    
                    drawList.AddLine(p1, p2, progressColor, thickness);
                }
            }
        }

        /// <summary>
        /// Draws a rotating spinner
        /// </summary>
        public static void AddSpinner(this ImDrawListPtr drawList, Vector2 center, float radius, 
            uint color, float speed = 2.0f, int segments = 8)
        {
            var time = (float)ImGui.GetTime();
            var rotation = time * speed;
            
            var angleStep = 2f * MathF.PI / segments;
            
            for (int i = 0; i < segments; i++)
            {
                var alpha = (float)(i + 1) / segments;
                var angle = rotation + i * angleStep;
                
                var start = center + new Vector2(MathF.Cos(angle), MathF.Sin(angle)) * (radius * 0.5f);
                var end = center + new Vector2(MathF.Cos(angle), MathF.Sin(angle)) * radius;
                
                var segmentColor = ColorUtils.WithAlpha(color, alpha);
                drawList.AddLine(start, end, segmentColor, 2.0f);
            }
        }
    }
}
