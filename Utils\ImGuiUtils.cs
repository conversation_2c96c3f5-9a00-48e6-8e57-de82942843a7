using System;
using System.Numerics;
using ImGuiNET;

namespace PvPLinePlugin.Utils
{
    public static class ImGuiUtils
    {
        /// <summary>
        /// Draws a gradient line between two points
        /// </summary>
        public static void AddGradientLine(this ImDrawListPtr drawList, Vector2 start, Vector2 end, 
            uint startColor, uint endColor, float thickness = 1.0f)
        {
            var distance = Vector2.Distance(start, end);
            var segments = Math.Max(2, (int)(distance / 10)); // One segment per 10 pixels
            
            for (int i = 0; i < segments; i++)
            {
                var t1 = (float)i / segments;
                var t2 = (float)(i + 1) / segments;
                
                var p1 = Vector2.Lerp(start, end, t1);
                var p2 = Vector2.Lerp(start, end, t2);
                var color = LerpColor(startColor, endColor, t1);
                
                drawList.AddLine(p1, p2, color, thickness);
            }
        }

        /// <summary>
        /// Draws a dashed line between two points
        /// </summary>
        public static void AddDashedLine(this ImDrawListPtr drawList, Vector2 start, Vector2 end, 
            uint color, float thickness = 1.0f, float dashLength = 5.0f, float gapLength = 3.0f)
        {
            var direction = Vector2.Normalize(end - start);
            var totalLength = Vector2.Distance(start, end);
            var cycleLength = dashLength + gapLength;
            
            var currentPos = start;
            var remainingLength = totalLength;
            
            while (remainingLength > 0)
            {
                var segmentLength = Math.Min(dashLength, remainingLength);
                var segmentEnd = currentPos + direction * segmentLength;
                
                drawList.AddLine(currentPos, segmentEnd, color, thickness);
                
                currentPos = segmentEnd + direction * gapLength;
                remainingLength -= cycleLength;
            }
        }

        /// <summary>
        /// Draws a pulsing circle with animated radius
        /// </summary>
        public static void AddPulsingCircle(this ImDrawListPtr drawList, Vector2 center, float baseRadius, 
            uint color, float pulseSpeed = 2.0f, float pulseAmount = 0.3f, float thickness = 1.0f)
        {
            var time = (float)ImGui.GetTime();
            var pulse = (float)(Math.Sin(time * pulseSpeed) * pulseAmount + 1.0f);
            var radius = baseRadius * pulse;
            
            drawList.AddCircle(center, radius, color, 0, thickness);
        }

        /// <summary>
        /// Draws a pulsing filled circle with animated radius and alpha
        /// </summary>
        public static void AddPulsingCircleFilled(this ImDrawListPtr drawList, Vector2 center, float baseRadius, 
            uint baseColor, float pulseSpeed = 2.0f, float pulseAmount = 0.3f)
        {
            var time = (float)ImGui.GetTime();
            var pulse = (float)(Math.Sin(time * pulseSpeed) * pulseAmount + 1.0f);
            var radius = baseRadius * pulse;
            
            // Animate alpha based on pulse
            var alpha = (byte)(255 * (1.0f - pulseAmount + pulseAmount * pulse));
            var color = (baseColor & 0x00FFFFFF) | ((uint)alpha << 24);
            
            drawList.AddCircleFilled(center, radius, color);
        }

        /// <summary>
        /// Draws an arrow pointing from start to end
        /// </summary>
        public static void AddArrow(this ImDrawListPtr drawList, Vector2 start, Vector2 end, 
            uint color, float thickness = 2.0f, float arrowSize = 10.0f)
        {
            // Draw main line
            drawList.AddLine(start, end, color, thickness);
            
            // Calculate arrow head
            var direction = Vector2.Normalize(end - start);
            var perpendicular = new Vector2(-direction.Y, direction.X);
            
            var arrowPoint1 = end - direction * arrowSize + perpendicular * (arrowSize * 0.5f);
            var arrowPoint2 = end - direction * arrowSize - perpendicular * (arrowSize * 0.5f);
            
            // Draw arrow head
            drawList.AddLine(end, arrowPoint1, color, thickness);
            drawList.AddLine(end, arrowPoint2, color, thickness);
        }

        /// <summary>
        /// Draws a directional arrow that points towards a target when off-screen
        /// </summary>
        public static void AddScreenEdgeArrow(this ImDrawListPtr drawList, Vector2 screenCenter, Vector2 targetDirection, 
            uint color, float arrowSize = 15.0f, float distanceFromEdge = 50.0f)
        {
            var screenSize = ImGui.GetIO().DisplaySize;
            var direction = Vector2.Normalize(targetDirection);
            
            // Calculate intersection with screen edges
            var edgePoint = CalculateScreenEdgeIntersection(screenCenter, direction, screenSize, distanceFromEdge);
            
            // Draw arrow pointing towards target
            var arrowStart = edgePoint - direction * arrowSize;
            AddArrow(drawList, arrowStart, edgePoint, color, 2.0f, arrowSize * 0.7f);
        }

        /// <summary>
        /// Interpolates between two colors
        /// </summary>
        public static uint LerpColor(uint colorA, uint colorB, float t)
        {
            t = Math.Clamp(t, 0.0f, 1.0f);
            
            var aR = (byte)(colorA & 0xFF);
            var aG = (byte)((colorA >> 8) & 0xFF);
            var aB = (byte)((colorA >> 16) & 0xFF);
            var aA = (byte)((colorA >> 24) & 0xFF);
            
            var bR = (byte)(colorB & 0xFF);
            var bG = (byte)((colorB >> 8) & 0xFF);
            var bB = (byte)((colorB >> 16) & 0xFF);
            var bA = (byte)((colorB >> 24) & 0xFF);
            
            var r = (byte)(aR + (bR - aR) * t);
            var g = (byte)(aG + (bG - aG) * t);
            var b = (byte)(aB + (bB - aB) * t);
            var a = (byte)(aA + (bA - aA) * t);
            
            return (uint)(r | (g << 8) | (b << 16) | (a << 24));
        }

        /// <summary>
        /// Calculates where a ray intersects the screen edge
        /// </summary>
        private static Vector2 CalculateScreenEdgeIntersection(Vector2 center, Vector2 direction, Vector2 screenSize, float margin)
        {
            var bounds = new Vector4(margin, margin, screenSize.X - margin, screenSize.Y - margin);
            
            // Calculate intersections with each edge
            var intersections = new Vector2[4];
            var validIntersections = new bool[4];
            
            // Left edge
            if (direction.X != 0)
            {
                var t = (bounds.X - center.X) / direction.X;
                var y = center.Y + direction.Y * t;
                if (t > 0 && y >= bounds.Y && y <= bounds.W)
                {
                    intersections[0] = new Vector2(bounds.X, y);
                    validIntersections[0] = true;
                }
            }
            
            // Right edge
            if (direction.X != 0)
            {
                var t = (bounds.Z - center.X) / direction.X;
                var y = center.Y + direction.Y * t;
                if (t > 0 && y >= bounds.Y && y <= bounds.W)
                {
                    intersections[1] = new Vector2(bounds.Z, y);
                    validIntersections[1] = true;
                }
            }
            
            // Top edge
            if (direction.Y != 0)
            {
                var t = (bounds.Y - center.Y) / direction.Y;
                var x = center.X + direction.X * t;
                if (t > 0 && x >= bounds.X && x <= bounds.Z)
                {
                    intersections[2] = new Vector2(x, bounds.Y);
                    validIntersections[2] = true;
                }
            }
            
            // Bottom edge
            if (direction.Y != 0)
            {
                var t = (bounds.W - center.Y) / direction.Y;
                var x = center.X + direction.X * t;
                if (t > 0 && x >= bounds.X && x <= bounds.Z)
                {
                    intersections[3] = new Vector2(x, bounds.W);
                    validIntersections[3] = true;
                }
            }
            
            // Find closest valid intersection
            var closestDistance = float.MaxValue;
            var closestPoint = center;
            
            for (int i = 0; i < 4; i++)
            {
                if (validIntersections[i])
                {
                    var distance = Vector2.Distance(center, intersections[i]);
                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestPoint = intersections[i];
                    }
                }
            }
            
            return closestPoint;
        }
    }
}
