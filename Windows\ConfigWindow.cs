using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;
    private Plugin Plugin;

    public ConfigWindow(Plugin plugin) : base("PvP Line Plugin Configuration###PvPLinePluginConfig")
    {
        Flags = ImGuiWindowFlags.NoCollapse;

        Size = new Vector2(650, 700);
        SizeCondition = ImGuiCond.FirstUseEver;

        Configuration = plugin.Configuration;
        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        // Main enable/disable at the top
        var enabled = Configuration.Enabled;
        if (ImGui.Checkbox("Enable PvP Lines", ref enabled))
        {
            Configuration.Enabled = enabled;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Master on/off switch for the entire plugin.\nDisable when you don't want any lines displayed.");
        }

        ImGui.Separator();
        ImGui.Spacing();

        // Tab bar for organized settings
        if (ImGui.BeginTabBar("PvPLinePluginTabs"))
        {
            DrawAppearanceTab();
            DrawDistanceTab();
            DrawPvPSettingsTab();
            DrawEnemyAllyTab();
            DrawJobRoleTab();
            DrawLowHealthTab();
            DrawPlayerListTab();

            ImGui.EndTabBar();
        }
    }

    private void DrawAppearanceTab()
    {
        if (ImGui.BeginTabItem("Appearance"))
        {
            ImGui.TextWrapped("Customize how enemy indicators look on your screen.");
            ImGui.Spacing();

            // Rendering System Selection
            ImGui.Text("Rendering System:");
            var useSplatoon = Configuration.UseSplatoonRendering;
            if (ImGui.Checkbox("Use Enhanced 3D Rendering (Splatoon)", ref useSplatoon))
            {
                Configuration.UseSplatoonRendering = useSplatoon;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Enable advanced 3D world indicators using Splatoon API.\nProvides better visual effects and 3D positioning.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Indicator Type Selection
            ImGui.Text("Indicator Type:");
            var currentIndicator = (int)Configuration.IndicatorType;

            string[] indicatorNames;
            string[] indicatorDescriptions;

            if (Configuration.UseSplatoonRendering)
            {
                indicatorNames = new[] {
                    "3D World Circles", "3D World Markers", "VFX Omens", "VFX Tethers",
                    "Ground Markers", "3D Beams", "Directional Cones", "3D Spheres",
                    "Hybrid: Lines + Markers", "Hybrid: Icons + Beams", "Hybrid: Advanced Combo"
                };
                indicatorDescriptions = new[] {
                    "Circular indicators in 3D world space",
                    "Square/rectangular markers in 3D space",
                    "Visual effects at target locations",
                    "Tether lines between you and targets",
                    "Markers placed on the ground",
                    "3D beam lines from you to targets",
                    "Cone shapes pointing toward targets",
                    "Spherical indicators around targets",
                    "Combines traditional lines with 3D markers",
                    "Combines icons with 3D beams",
                    "Advanced combination of multiple effects"
                };
                // Adjust currentIndicator for Splatoon types
                if (currentIndicator < 10) currentIndicator = 0; // Default to first Splatoon type
                else currentIndicator -= 10; // Offset for Splatoon enum values
            }
            else
            {
                indicatorNames = new[] {
                    "Lines", "Outlines", "Nameplates", "Icons", "Directional Arrows",
                    "Health Bars", "Combination", "Screen Edge Arrows", "Gradient Lines", "Animated Pulse"
                };
                indicatorDescriptions = new[] {
                    "Simple lines pointing to targets",
                    "Outlined shapes around targets",
                    "Text nameplates above targets",
                    "Icon symbols for targets",
                    "Arrows pointing toward targets",
                    "Health bar displays",
                    "Multiple indicator types combined",
                    "Arrows at screen edges for off-screen targets",
                    "Lines with color gradients",
                    "Pulsing animated indicators"
                };
                // Keep currentIndicator as-is for traditional types
                if (currentIndicator >= 10) currentIndicator = 0; // Default to first traditional type
            }

            if (ImGui.Combo("##IndicatorType", ref currentIndicator, indicatorNames, indicatorNames.Length))
            {
                if (Configuration.UseSplatoonRendering)
                {
                    Configuration.IndicatorType = (IndicatorType)(currentIndicator + 10); // Offset for Splatoon types
                }
                else
                {
                    Configuration.IndicatorType = (IndicatorType)currentIndicator;
                }
                Configuration.Save();
            }

            // Show description for selected indicator type
            if (currentIndicator >= 0 && currentIndicator < indicatorDescriptions.Length)
            {
                ImGui.TextWrapped($"Description: {indicatorDescriptions[currentIndicator]}");
            }
            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Show settings based on selected indicator type and rendering system
            if (Configuration.UseSplatoonRendering)
            {
                DrawSplatoonSettings();
            }
            else
            {
                DrawTraditionalIndicatorSettings();
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawLineSettings()
    {
        ImGui.Text("Line Settings:");

        // Group related settings to reduce Save() calls
        var lineColor = Configuration.LineColor;
        var lineThickness = Configuration.LineThickness;
        var pulseIndicators = Configuration.PulseIndicators;
        var hasChanges = false;

        if (ImGui.ColorEdit4("Line Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Choose the color and transparency of lines.\nRecommended: Red for aggression, Yellow for visibility, Blue for subtlety.\nLower alpha (transparency) makes lines less intrusive.");
        }

        if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1.0f, 10.0f))
        {
            Configuration.LineThickness = lineThickness;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Controls how thick the lines appear.\n1-2px: Subtle, minimal\n3-4px: Good balance\n5-7px: High visibility\n8-10px: Maximum visibility but may be distracting");
        }

        if (ImGui.Checkbox("Pulse Effect", ref pulseIndicators))
        {
            Configuration.PulseIndicators = pulseIndicators;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Add a pulsing animation effect to lines for better visibility.");
        }

        // Enhanced line options
        var useGradientLines = Configuration.UseGradientLines;
        if (ImGui.Checkbox("Use Gradient Lines", ref useGradientLines))
        {
            Configuration.UseGradientLines = useGradientLines;
            hasChanges = true;
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Lines will fade from one color to another along their length.");
        }

        if (Configuration.UseGradientLines)
        {
            ImGui.Indent();
            var gradientEndColor = Configuration.GradientEndColor;
            if (ImGui.ColorEdit4("Gradient End Color", ref gradientEndColor))
            {
                Configuration.GradientEndColor = gradientEndColor;
                hasChanges = true;
            }
            ImGui.Unindent();
        }

        var useDashedLines = Configuration.UseDashedLines;
        if (ImGui.Checkbox("Use Dashed Lines", ref useDashedLines))
        {
            Configuration.UseDashedLines = useDashedLines;
            hasChanges = true;
        }

        if (Configuration.UseDashedLines)
        {
            ImGui.Indent();
            var dashLength = Configuration.DashLength;
            if (ImGui.SliderFloat("Dash Length", ref dashLength, 2.0f, 15.0f))
            {
                Configuration.DashLength = dashLength;
                hasChanges = true;
            }

            var gapLength = Configuration.GapLength;
            if (ImGui.SliderFloat("Gap Length", ref gapLength, 1.0f, 10.0f))
            {
                Configuration.GapLength = gapLength;
                hasChanges = true;
            }
            ImGui.Unindent();
        }

        // Save once if any changes were made
        if (hasChanges)
        {
            Configuration.Save();
        }
    }

    private void DrawOutlineSettings()
    {
        ImGui.Text("Outline Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Outline Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var outlineThickness = Configuration.OutlineThickness;
        if (ImGui.SliderFloat("Outline Thickness", ref outlineThickness, 1.0f, 10.0f))
        {
            Configuration.OutlineThickness = outlineThickness;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Thickness of the outline around enemy players.");
        }
    }

    private void DrawNameplateSettings()
    {
        ImGui.Text("Nameplate Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Nameplate Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Enhanced nameplates will appear above enemy players with job icons and health information.");
    }

    private void DrawIconSettings()
    {
        ImGui.Text("Icon Settings:");

        var iconSize = Configuration.IconSize;
        if (ImGui.SliderFloat("Icon Size", ref iconSize, 10.0f, 50.0f))
        {
            Configuration.IconSize = iconSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Icon Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Small icons will appear on screen showing enemy positions and job types.");
    }

    private void DrawArrowSettings()
    {
        ImGui.Text("Directional Arrow Settings:");

        var arrowSize = Configuration.ArrowSize;
        if (ImGui.SliderFloat("Arrow Size", ref arrowSize, 10.0f, 40.0f))
        {
            Configuration.ArrowSize = arrowSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Arrow Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Arrows will point toward off-screen enemies from the edges of your screen.");
    }

    private void DrawHealthBarSettings()
    {
        ImGui.Text("Health Bar Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Health Bar Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Floating health bars will appear above enemy players showing their current HP.");
    }

    private void DrawCombinationSettings()
    {
        ImGui.Text("Combination Settings:");
        ImGui.TextWrapped("Mix and match multiple indicator types. Configure each type above by temporarily switching to it.");

        var showDirectionalArrows = Configuration.ShowDirectionalArrows;
        if (ImGui.Checkbox("Include Directional Arrows", ref showDirectionalArrows))
        {
            Configuration.ShowDirectionalArrows = showDirectionalArrows;
            Configuration.Save();
        }

        ImGui.TextWrapped("Note: Combination mode will use settings from each individual indicator type.");
    }

    private void DrawDistanceTab()
    {
        if (ImGui.BeginTabItem("Distance"))
        {
            ImGui.TextWrapped("Control how far lines reach and distance information display.");
            ImGui.Spacing();

            var maxDistance = Configuration.MaxDistance;
            if (ImGui.SliderFloat("Max Distance (yalms)", ref maxDistance, 10.0f, 100.0f))
            {
                Configuration.MaxDistance = maxDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Only draws lines to enemies within this distance.\n20-30y: Close combat (melee DPS, tanks)\n40-50y: Balanced for most jobs\n60-80y: Long-range (casters, ranged DPS)\n100y: Maximum awareness (may cause clutter)");
            }

            var showDistance = Configuration.ShowDistance;
            if (ImGui.Checkbox("Show Distance Text", ref showDistance))
            {
                Configuration.ShowDistance = showDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays actual distance in yalms next to each line.\nUseful for learning optimal engagement ranges.\nDisable to reduce screen clutter.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawPvPSettingsTab()
    {
        if (ImGui.BeginTabItem("PvP Settings"))
        {
            ImGui.TextWrapped("Configure when and how the plugin activates in PvP content.");
            ImGui.Spacing();

            var onlyInPvP = Configuration.OnlyInPvP;
            if (ImGui.Checkbox("Only Show in PvP Zones", ref onlyInPvP))
            {
                Configuration.OnlyInPvP = onlyInPvP;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Restricts plugin to PvP areas only (Frontlines, Rival Wings, Crystalline Conflict, Wolves' Den).\nRecommended: Keep enabled to avoid marking friendly players as enemies in PvE content.");
            }

            var showInCombatOnly = Configuration.ShowInCombatOnly;
            if (ImGui.Checkbox("Only Show During Combat", ref showInCombatOnly))
            {
                Configuration.ShowInCombatOnly = showInCombatOnly;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Lines only appear when you're actively in combat (hotbars turn red).\nEnable: Reduces visual noise during downtime\nDisable: Constant enemy awareness and positioning");
            }

            var showPlayerNames = Configuration.ShowPlayerNames;
            if (ImGui.Checkbox("Show Enemy Player Names", ref showPlayerNames))
            {
                Configuration.ShowPlayerNames = showPlayerNames;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays character names above enemy players.\nUseful for recognizing specific players and coordinating with team.\nDisable to reduce screen clutter.");
            }

            var showStatusEffects = Configuration.ShowStatusEffects;
            if (ImGui.Checkbox("Show Status Effects", ref showStatusEffects))
            {
                Configuration.ShowStatusEffects = showStatusEffects;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays important buffs and debuffs on players.\nShows crowd control, vulnerability, damage buffs, and other tactical information.\nHelps identify opportunities and threats.");
            }

            if (Configuration.ShowStatusEffects)
            {
                ImGui.Indent();
                var showOnlyImportant = Configuration.ShowOnlyImportantStatus;
                if (ImGui.Checkbox("Show Only Negative Effects", ref showOnlyImportant))
                {
                    Configuration.ShowOnlyImportantStatus = showOnlyImportant;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("When enabled, only shows debuffs and negative status effects.\nWhen disabled, shows both buffs and debuffs.\nRecommended: Enable to reduce visual clutter.");
                }
                ImGui.Unindent();
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showDefensiveBuffs = Configuration.ShowDefensiveBuffs;
            if (ImGui.Checkbox("Show Defensive Buff Indicators", ref showDefensiveBuffs))
            {
                Configuration.ShowDefensiveBuffs = showDefensiveBuffs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Shows special indicators for enemies using defensive abilities.\n" +
                                "• GUARD - AVOID: Enemy has 90% damage reduction (blue line)\n" +
                                "• HEALING: Enemy is using Recuperate (cyan line)\n" +
                                "• DEFENSIVE: Enemy has other defensive buffs (light blue line)\n" +
                                "Helps you avoid wasting damage on protected enemies.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawEnemyAllyTab()
    {
        if (ImGui.BeginTabItem("Enemy/Ally"))
        {
            ImGui.TextWrapped("Configure how the plugin distinguishes between enemies and allies.");
            ImGui.Spacing();

            var showEnemies = Configuration.ShowEnemies;
            if (ImGui.Checkbox("Show Lines to Enemies", ref showEnemies))
            {
                Configuration.ShowEnemies = showEnemies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to enemy players.\nThis is the main feature for tracking opponents in PvP.");
            }

            var showAllies = Configuration.ShowAllies;
            if (ImGui.Checkbox("Show Lines to Allies", ref showAllies))
            {
                Configuration.ShowAllies = showAllies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to allied players (party/alliance members).\nUseful for keeping track of your team's position.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showIndicator = Configuration.ShowAllyEnemyIndicator;
            if (ImGui.Checkbox("Show 'ALLY' / 'ENEMY' Labels", ref showIndicator))
            {
                Configuration.ShowAllyEnemyIndicator = showIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display text labels above players indicating if they are allies or enemies.\nHelps quickly identify player status in chaotic battles.");
            }

            ImGui.Spacing();
            ImGui.Text("Ally Line Appearance:");

            var allyLineColor = Configuration.AllyLineColor;
            if (ImGui.ColorEdit4("Ally Line Color", ref allyLineColor))
            {
                Configuration.AllyLineColor = allyLineColor;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Color for lines to allied players.\nDefault: Green to indicate friendly status.");
            }

            var allyLineThickness = Configuration.AllyLineThickness;
            if (ImGui.SliderFloat("Ally Line Thickness", ref allyLineThickness, 1.0f, 10.0f))
            {
                Configuration.AllyLineThickness = allyLineThickness;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Thickness of lines to allied players.\nCan be different from enemy line thickness for easy distinction.");
            }

            var differentColors = Configuration.DifferentColorsForAllies;
            if (ImGui.Checkbox("Use Different Colors for Allies", ref differentColors))
            {
                Configuration.DifferentColorsForAllies = differentColors;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("When enabled, ally lines will use a blend of role colors and ally color.\nWhen disabled, all ally lines use the same ally color.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            ImGui.TextWrapped("Status Effect Information:");
            ImGui.BulletText("Vulnerable enemies get orange lines for easy targeting");
            ImGui.BulletText("Crowd controlled enemies have pulsing status text");
            ImGui.BulletText("Status effects show remaining duration when available");
            ImGui.BulletText("Priority effects (stuns, vulnerability) are shown first");

            ImGui.Spacing();
            ImGui.TextWrapped("Note: Ally detection works for party and alliance members. In some PvP modes, additional allies (like Grand Company members) may not be automatically detected.");

            ImGui.EndTabItem();
        }
    }

    private void DrawJobRoleTab()
    {
        if (ImGui.BeginTabItem("Jobs & Roles"))
        {
            ImGui.TextWrapped("Configure job and role detection for tactical PvP advantage.");
            ImGui.Spacing();

            var showPlayerJobs = Configuration.ShowPlayerJobs;
            if (ImGui.Checkbox("Show Enemy Jobs/Roles", ref showPlayerJobs))
            {
                Configuration.ShowPlayerJobs = showPlayerJobs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays job or role information above enemy players.\nHelps identify threats: Healers (priority targets), Tanks (high HP), DPS types.\nEssential for tactical PvP play.");
            }

            if (Configuration.ShowPlayerJobs)
            {
                ImGui.Indent();
                var showJobIcons = Configuration.ShowJobIcons;
                if (ImGui.Checkbox("Show Job Abbreviations (vs Role Names)", ref showJobIcons))
                {
                    Configuration.ShowJobIcons = showJobIcons;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Job Abbreviations: WHM, PLD, NIN, etc.\nRole Names: HEAL, TANK, MDPS, etc.\nJob abbreviations are more specific but take more space.");
                }
                ImGui.Unindent();
            }

            var colorCodeByRole = Configuration.ColorCodeByRole;
            if (ImGui.Checkbox("Color Lines by Role", ref colorCodeByRole))
            {
                Configuration.ColorCodeByRole = colorCodeByRole;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Colors lines based on enemy role:\nBlue = Tanks, Green = Healers, Red = Melee DPS\nOrange = Ranged Physical DPS, Purple = Magical DPS\nOverrides the custom line color setting when enabled.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Role color preview
            ImGui.TextWrapped("Role Color Preview:");
            ImGui.Spacing();

            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Tank), "■ TANK - Tanks (PLD, WAR, DRK, GNB)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Healer), "■ HEAL - Healers (WHM, SCH, AST, SGE)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MeleeDPS), "■ MDPS - Melee DPS (MNK, DRG, NIN, SAM, RPR, VPR)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.PhysicalRangedDPS), "■ PDPS - Physical Ranged (BRD, MCH, DNC)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MagicalRangedDPS), "■ CDPS - Magical Ranged (BLM, SMN, RDM, PCT)");

            ImGui.EndTabItem();
        }
    }

    private void DrawLowHealthTab()
    {
        if (ImGui.BeginTabItem("Low Health"))
        {
            ImGui.TextWrapped("Configure killable target detection and visual indicators.");
            ImGui.Spacing();

            var showLowHealthIndicator = Configuration.ShowLowHealthIndicator;
            if (ImGui.Checkbox("Enable Low Health Indicator", ref showLowHealthIndicator))
            {
                Configuration.ShowLowHealthIndicator = showLowHealthIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Highlights enemies with low health as potential kill targets.\nChanges line color, thickness, and adds health information.");
            }

            if (Configuration.ShowLowHealthIndicator)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                var lowHealthThreshold = Configuration.LowHealthThreshold;
                if (ImGui.SliderFloat("Low Health Threshold (%)", ref lowHealthThreshold, 5.0f, 50.0f))
                {
                    Configuration.LowHealthThreshold = lowHealthThreshold;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Health percentage below which enemies are marked as 'killable'.\n10-15%: Very low, almost dead\n20-25%: Good for burst combos\n30-40%: Early kill attempt threshold");
                }

                var lowHealthLineColor = Configuration.LowHealthLineColor;
                if (ImGui.ColorEdit4("Low Health Line Color", ref lowHealthLineColor))
                {
                    Configuration.LowHealthLineColor = lowHealthLineColor;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Color for lines to low health enemies.\nRecommended: Gold/Yellow for high visibility, Red for urgency.");
                }

                var lowHealthLineThickness = Configuration.LowHealthLineThickness;
                if (ImGui.SliderFloat("Low Health Line Thickness", ref lowHealthLineThickness, 2.0f, 15.0f))
                {
                    Configuration.LowHealthLineThickness = lowHealthLineThickness;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Thickness of lines to low health enemies.\nThicker lines make killable targets more obvious.");
                }

                var showHealthPercentage = Configuration.ShowHealthPercentage;
                if (ImGui.Checkbox("Show Health Percentage", ref showHealthPercentage))
                {
                    Configuration.ShowHealthPercentage = showHealthPercentage;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Shows exact health percentage (e.g., '15% HP') vs just 'KILLABLE'.\nUseful for precise timing of finishing moves.");
                }

                var pulseKillableTargets = Configuration.PulseKillableTargets;
                if (ImGui.Checkbox("Pulse Killable Targets", ref pulseKillableTargets))
                {
                    Configuration.PulseKillableTargets = pulseKillableTargets;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Makes lines and text for killable targets pulse/animate.\nDraws more attention but may be distracting.");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                // Usage tips
                ImGui.TextWrapped("Usage Tips:");
                ImGui.BulletText("Low health targets appear with different colored, thicker lines");
                ImGui.BulletText("Health info appears above the enemy player");
                ImGui.BulletText("Use this to prioritize finishing moves and burst combos");
                ImGui.BulletText("Adjust threshold based on your job's burst potential");
                ImGui.BulletText("Healers at low health are highest priority targets");
            }

            ImGui.EndTabItem();
        }
    }



    private void DrawPlayerListTab()
    {
        if (ImGui.BeginTabItem("Player List"))
        {
            ImGui.TextWrapped("Show a window with all nearby players for easy tracking. Perfect for Crystalline Conflict matches.");
            ImGui.Spacing();

            var showPlayerList = Configuration.ShowPlayerList;
            if (ImGui.Checkbox("Show Player List Window", ref showPlayerList))
            {
                Configuration.ShowPlayerList = showPlayerList;
                Configuration.Save();

                // Toggle the window visibility
                if (showPlayerList)
                    Plugin.TogglePlayerListUI();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Show a separate window listing all nearby players with their status, health, and distance.");
            }

            if (Configuration.ShowPlayerList)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Display Options:");

                var playerListShowAllies = Configuration.PlayerListShowAllies;
                if (ImGui.Checkbox("Show Allies Tab", ref playerListShowAllies))
                {
                    Configuration.PlayerListShowAllies = playerListShowAllies;
                    Configuration.Save();
                }

                var playerListShowEnemies = Configuration.PlayerListShowEnemies;
                if (ImGui.Checkbox("Show Enemies Tab", ref playerListShowEnemies))
                {
                    Configuration.PlayerListShowEnemies = playerListShowEnemies;
                    Configuration.Save();
                }

                ImGui.Spacing();

                var playerListShowDistance = Configuration.PlayerListShowDistance;
                if (ImGui.Checkbox("Show Distance", ref playerListShowDistance))
                {
                    Configuration.PlayerListShowDistance = playerListShowDistance;
                    Configuration.Save();
                }

                var playerListShowHealth = Configuration.PlayerListShowHealth;
                if (ImGui.Checkbox("Show Health Percentage", ref playerListShowHealth))
                {
                    Configuration.PlayerListShowHealth = playerListShowHealth;
                    Configuration.Save();
                }

                var playerListShowJob = Configuration.PlayerListShowJob;
                if (ImGui.Checkbox("Show Job/Role", ref playerListShowJob))
                {
                    Configuration.PlayerListShowJob = playerListShowJob;
                    Configuration.Save();
                }

                var playerListShowStatus = Configuration.PlayerListShowStatus;
                if (ImGui.Checkbox("Show Status Effects", ref playerListShowStatus))
                {
                    Configuration.PlayerListShowStatus = playerListShowStatus;
                    Configuration.Save();
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                ImGui.Text("Behavior Settings:");

                var playerListMaxDistance = Configuration.PlayerListMaxDistance;
                if (ImGui.SliderFloat("Max Distance (yalms)", ref playerListMaxDistance, 30.0f, 200.0f))
                {
                    Configuration.PlayerListMaxDistance = playerListMaxDistance;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Maximum distance to show players in the list.\n100y covers most of a Crystalline Conflict map.");
                }

                var playerListAutoHide = Configuration.PlayerListAutoHide;
                if (ImGui.Checkbox("Auto-hide outside PvP", ref playerListAutoHide))
                {
                    Configuration.PlayerListAutoHide = playerListAutoHide;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Automatically hide the player list when not in PvP zones.");
                }



                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                if (ImGui.Button("Open Player List Window"))
                {
                    Plugin.TogglePlayerListUI();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Open the player list window to see it in action.");
                }

                ImGui.SameLine();
                if (ImGui.Button("Reset Window Position"))
                {
                    // This would reset the window position - implementation depends on how you want to handle it
                    ImGui.OpenPopup("ResetInfo");
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Reset the player list window to its default position.");
                }

                if (ImGui.BeginPopup("ResetInfo"))
                {
                    ImGui.Text("Close and reopen the player list window to reset its position.");
                    ImGui.EndPopup();
                }
            }

            ImGui.EndTabItem();
        }
    }

    // New settings methods for enhanced indicator types
    private void DrawWorldCircleSettings()
    {
        ImGui.Text("3D World Circle Settings:");

        var use3DWorldSpace = Configuration.Use3DWorldSpace;
        if (ImGui.Checkbox("Enable 3D World Space Rendering", ref use3DWorldSpace))
        {
            Configuration.Use3DWorldSpace = use3DWorldSpace;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Renders circles in 3D world space around enemies instead of 2D screen overlays.");
        }

        var showGroundCircles = Configuration.ShowGroundCircles;
        if (ImGui.Checkbox("Show Ground Circles", ref showGroundCircles))
        {
            Configuration.ShowGroundCircles = showGroundCircles;
            Configuration.Save();
        }

        var groundCircleRadius = Configuration.GroundCircleRadius;
        if (ImGui.SliderFloat("Circle Radius", ref groundCircleRadius, 0.5f, 10.0f))
        {
            Configuration.GroundCircleRadius = groundCircleRadius;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Circle Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("3D circles will appear around enemies in world space, visible from any angle.");
    }

    private void DrawVfxOmenSettings()
    {
        ImGui.Text("VFX Omen Settings:");

        var enableVfxEffects = Configuration.EnableVfxEffects;
        if (ImGui.Checkbox("Enable VFX Effects", ref enableVfxEffects))
        {
            Configuration.EnableVfxEffects = enableVfxEffects;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Uses the game's native VFX system to create omen-style indicators around enemies.");
        }

        var vfxOpacity = Configuration.VfxOpacity;
        if (ImGui.SliderFloat("VFX Opacity", ref vfxOpacity, 0.1f, 1.0f))
        {
            Configuration.VfxOpacity = vfxOpacity;
            Configuration.Save();
        }

        var groundCircleRadius = Configuration.GroundCircleRadius;
        if (ImGui.SliderFloat("Omen Radius", ref groundCircleRadius, 1.0f, 15.0f))
        {
            Configuration.GroundCircleRadius = groundCircleRadius;
            Configuration.Save();
        }

        ImGui.TextWrapped("Creates boss-style omen effects around enemies using the game's VFX system.");
    }

    private void DrawVfxTetherSettings()
    {
        ImGui.Text("VFX Tether Settings:");

        var enableTethers = Configuration.EnableTethers;
        if (ImGui.Checkbox("Enable Tethers", ref enableTethers))
        {
            Configuration.EnableTethers = enableTethers;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Creates beam/tether effects connecting you to enemies using the game's VFX system.");
        }

        var tetherVfxPath = Configuration.TetherVfxPath;
        if (ImGui.InputText("Tether VFX Path", ref tetherVfxPath, 100))
        {
            Configuration.TetherVfxPath = tetherVfxPath;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("VFX path for tether effects. Default: chn_nomal01f");
        }

        var vfxOpacity = Configuration.VfxOpacity;
        if (ImGui.SliderFloat("Tether Opacity", ref vfxOpacity, 0.1f, 1.0f))
        {
            Configuration.VfxOpacity = vfxOpacity;
            Configuration.Save();
        }

        ImGui.TextWrapped("Creates channeling-style beam effects between you and enemies.");
    }

    private void DrawGroundMarkerSettings()
    {
        ImGui.Text("Ground Marker Settings:");

        var showGroundCircles = Configuration.ShowGroundCircles;
        if (ImGui.Checkbox("Show Ground Markers", ref showGroundCircles))
        {
            Configuration.ShowGroundCircles = showGroundCircles;
            Configuration.Save();
        }

        var groundCircleRadius = Configuration.GroundCircleRadius;
        if (ImGui.SliderFloat("Marker Size", ref groundCircleRadius, 0.5f, 5.0f))
        {
            Configuration.GroundCircleRadius = groundCircleRadius;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Marker Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var useDistanceBasedColors = Configuration.UseDistanceBasedColors;
        if (ImGui.Checkbox("Use Distance-Based Colors", ref useDistanceBasedColors))
        {
            Configuration.UseDistanceBasedColors = useDistanceBasedColors;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Markers become more intense/visible the closer enemies are to you.");
        }

        ImGui.TextWrapped("Places markers on the ground at enemy positions, visible through terrain.");
    }

    private void DrawFloatingIconSettings()
    {
        ImGui.Text("Floating Icon Settings:");

        var iconSize = Configuration.IconSize;
        if (ImGui.SliderFloat("Icon Size", ref iconSize, 10.0f, 100.0f))
        {
            Configuration.IconSize = iconSize;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Icon Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var showPlayerJobs = Configuration.ShowPlayerJobs;
        if (ImGui.Checkbox("Show Job Icons", ref showPlayerJobs))
        {
            Configuration.ShowPlayerJobs = showPlayerJobs;
            Configuration.Save();
        }

        ImGui.TextWrapped("3D icons that float above enemies showing their job and status.");
    }

    private void DrawScreenEdgeArrowSettings()
    {
        ImGui.Text("Screen Edge Arrow Settings:");

        var showScreenEdgeArrows = Configuration.ShowScreenEdgeArrows;
        if (ImGui.Checkbox("Show Screen Edge Arrows", ref showScreenEdgeArrows))
        {
            Configuration.ShowScreenEdgeArrows = showScreenEdgeArrows;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Shows arrows at screen edges pointing towards off-screen enemies.");
        }

        var arrowSize = Configuration.ArrowSize;
        if (ImGui.SliderFloat("Arrow Size", ref arrowSize, 10.0f, 50.0f))
        {
            Configuration.ArrowSize = arrowSize;
            Configuration.Save();
        }

        var screenEdgeMargin = Configuration.ScreenEdgeMargin;
        if (ImGui.SliderFloat("Edge Margin", ref screenEdgeMargin, 20.0f, 100.0f))
        {
            Configuration.ScreenEdgeMargin = screenEdgeMargin;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Distance from screen edge where arrows appear.");
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Arrow Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        ImGui.TextWrapped("Arrows appear at screen edges when enemies are off-screen, pointing in their direction.");
    }

    private void DrawGradientLineSettings()
    {
        ImGui.Text("Gradient Line Settings:");

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Start Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var gradientEndColor = Configuration.GradientEndColor;
        if (ImGui.ColorEdit4("End Color", ref gradientEndColor))
        {
            Configuration.GradientEndColor = gradientEndColor;
            Configuration.Save();
        }

        var lineThickness = Configuration.LineThickness;
        if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1.0f, 10.0f))
        {
            Configuration.LineThickness = lineThickness;
            Configuration.Save();
        }

        var useDistanceBasedColors = Configuration.UseDistanceBasedColors;
        if (ImGui.Checkbox("Distance-Based Gradient", ref useDistanceBasedColors))
        {
            Configuration.UseDistanceBasedColors = useDistanceBasedColors;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Gradient intensity changes based on distance to enemy.");
        }

        ImGui.TextWrapped("Lines with smooth color gradients from start to end point.");
    }

    private void DrawAnimatedPulseSettings()
    {
        ImGui.Text("Animated Pulse Settings:");

        var pulseSpeed = Configuration.PulseSpeed;
        if (ImGui.SliderFloat("Pulse Speed", ref pulseSpeed, 0.5f, 5.0f))
        {
            Configuration.PulseSpeed = pulseSpeed;
            Configuration.Save();
        }

        var pulseAmount = Configuration.PulseAmount;
        if (ImGui.SliderFloat("Pulse Amount", ref pulseAmount, 0.1f, 1.0f))
        {
            Configuration.PulseAmount = pulseAmount;
            Configuration.Save();
        }

        var animateOnLowHealth = Configuration.AnimateOnLowHealth;
        if (ImGui.Checkbox("Animate on Low Health", ref animateOnLowHealth))
        {
            Configuration.AnimateOnLowHealth = animateOnLowHealth;
            Configuration.Save();
        }

        var animateOnDefensiveBuff = Configuration.AnimateOnDefensiveBuff;
        if (ImGui.Checkbox("Animate on Defensive Buffs", ref animateOnDefensiveBuff))
        {
            Configuration.AnimateOnDefensiveBuff = animateOnDefensiveBuff;
            Configuration.Save();
        }

        var lineColor = Configuration.LineColor;
        if (ImGui.ColorEdit4("Pulse Color", ref lineColor))
        {
            Configuration.LineColor = lineColor;
            Configuration.Save();
        }

        var useHealthBasedColors = Configuration.UseHealthBasedColors;
        if (ImGui.Checkbox("Health-Based Colors", ref useHealthBasedColors))
        {
            Configuration.UseHealthBasedColors = useHealthBasedColors;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Pulse color changes based on enemy health percentage.");
        }

        ImGui.TextWrapped("Pulsing animated indicators with customizable speed and intensity.");
    }

    private void DrawSplatoonSettings()
    {
        ImGui.Text("Enhanced 3D Rendering Settings:");
        ImGui.Spacing();

        // Global Splatoon settings
        var splatoonWorldMarkersEnabled = Configuration.SplatoonWorldMarkersEnabled;
        if (ImGui.Checkbox("Enable World Markers", ref splatoonWorldMarkersEnabled))
        {
            Configuration.SplatoonWorldMarkersEnabled = splatoonWorldMarkersEnabled;
            Configuration.Save();
        }

        var splatoonMarkerRadius = Configuration.SplatoonMarkerRadius;
        if (ImGui.SliderFloat("Marker Radius", ref splatoonMarkerRadius, 0.5f, 10.0f))
        {
            Configuration.SplatoonMarkerRadius = splatoonMarkerRadius;
            Configuration.Save();
        }

        var splatoonMarkerHeight = Configuration.SplatoonMarkerHeight;
        if (ImGui.SliderFloat("Marker Height", ref splatoonMarkerHeight, 0.0f, 5.0f))
        {
            Configuration.SplatoonMarkerHeight = splatoonMarkerHeight;
            Configuration.Save();
        }

        ImGui.Spacing();
        ImGui.Separator();
        ImGui.Spacing();

        // Color settings
        ImGui.Text("Color Settings:");
        var enemyColor = Configuration.SplatoonMarkerColor;
        var enemyColorVec = new Vector4(
            ((enemyColor >> 0) & 0xFF) / 255f,
            ((enemyColor >> 8) & 0xFF) / 255f,
            ((enemyColor >> 16) & 0xFF) / 255f,
            ((enemyColor >> 24) & 0xFF) / 255f
        );
        if (ImGui.ColorEdit4("Enemy Marker Color", ref enemyColorVec))
        {
            Configuration.SplatoonMarkerColor = (uint)(
                ((byte)(enemyColorVec.W * 255) << 24) |
                ((byte)(enemyColorVec.Z * 255) << 16) |
                ((byte)(enemyColorVec.Y * 255) << 8) |
                (byte)(enemyColorVec.X * 255)
            );
            Configuration.Save();
        }

        var allyColor = Configuration.SplatoonAllyMarkerColor;
        var allyColorVec = new Vector4(
            ((allyColor >> 0) & 0xFF) / 255f,
            ((allyColor >> 8) & 0xFF) / 255f,
            ((allyColor >> 16) & 0xFF) / 255f,
            ((allyColor >> 24) & 0xFF) / 255f
        );
        if (ImGui.ColorEdit4("Ally Marker Color", ref allyColorVec))
        {
            Configuration.SplatoonAllyMarkerColor = (uint)(
                ((byte)(allyColorVec.W * 255) << 24) |
                ((byte)(allyColorVec.Z * 255) << 16) |
                ((byte)(allyColorVec.Y * 255) << 8) |
                (byte)(allyColorVec.X * 255)
            );
            Configuration.Save();
        }

        ImGui.Spacing();
        ImGui.Separator();
        ImGui.Spacing();

        // Type-specific settings
        switch (Configuration.IndicatorType)
        {
            case IndicatorType.SplatoonVfxOmens:
            case IndicatorType.SplatoonVfxTethers:
                DrawSplatoonVfxSettings();
                break;
            case IndicatorType.SplatoonBeams:
                DrawSplatoonBeamSettings();
                break;
            case IndicatorType.SplatoonCustomShapes:
                DrawSplatoonShapeSettings();
                break;
        }

        ImGui.Spacing();
        ImGui.Separator();
        ImGui.Spacing();

        // Performance settings
        ImGui.Text("Performance Settings:");
        var maxElements = Configuration.SplatoonMaxElements;
        if (ImGui.SliderInt("Max Elements", ref maxElements, 10, 100))
        {
            Configuration.SplatoonMaxElements = maxElements;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Maximum number of Splatoon elements to render at once.");
        }

        var limitByDistance = Configuration.SplatoonLimitByDistance;
        if (ImGui.Checkbox("Limit by Distance", ref limitByDistance))
        {
            Configuration.SplatoonLimitByDistance = limitByDistance;
            Configuration.Save();
        }

        if (Configuration.SplatoonLimitByDistance)
        {
            var maxRenderDistance = Configuration.SplatoonMaxRenderDistance;
            if (ImGui.SliderFloat("Max Render Distance", ref maxRenderDistance, 50.0f, 200.0f))
            {
                Configuration.SplatoonMaxRenderDistance = maxRenderDistance;
                Configuration.Save();
            }
        }
    }

    private void DrawSplatoonVfxSettings()
    {
        ImGui.Text("VFX Settings:");

        var vfxEnabled = Configuration.SplatoonVfxEnabled;
        if (ImGui.Checkbox("Enable VFX Effects", ref vfxEnabled))
        {
            Configuration.SplatoonVfxEnabled = vfxEnabled;
            Configuration.Save();
        }

        var vfxPath = Configuration.SplatoonVfxPath;
        if (ImGui.InputText("VFX Path", ref vfxPath, 200))
        {
            Configuration.SplatoonVfxPath = vfxPath;
            Configuration.Save();
        }

        var vfxScale = Configuration.SplatoonVfxScale;
        if (ImGui.SliderFloat("VFX Scale", ref vfxScale, 0.1f, 3.0f))
        {
            Configuration.SplatoonVfxScale = vfxScale;
            Configuration.Save();
        }

        var vfxDuration = Configuration.SplatoonVfxDuration;
        if (ImGui.SliderFloat("VFX Duration", ref vfxDuration, 1.0f, 10.0f))
        {
            Configuration.SplatoonVfxDuration = vfxDuration;
            Configuration.Save();
        }
    }

    private void DrawSplatoonBeamSettings()
    {
        ImGui.Text("Beam Settings:");

        var beamsEnabled = Configuration.SplatoonBeamsEnabled;
        if (ImGui.Checkbox("Enable Beams", ref beamsEnabled))
        {
            Configuration.SplatoonBeamsEnabled = beamsEnabled;
            Configuration.Save();
        }

        var beamThickness = Configuration.SplatoonBeamThickness;
        if (ImGui.SliderFloat("Beam Thickness", ref beamThickness, 0.1f, 2.0f))
        {
            Configuration.SplatoonBeamThickness = beamThickness;
            Configuration.Save();
        }
    }

    private void DrawSplatoonShapeSettings()
    {
        ImGui.Text("Custom Shape Settings:");

        var shapesEnabled = Configuration.SplatoonCustomShapesEnabled;
        if (ImGui.Checkbox("Enable Custom Shapes", ref shapesEnabled))
        {
            Configuration.SplatoonCustomShapesEnabled = shapesEnabled;
            Configuration.Save();
        }

        var shapeScale = Configuration.SplatoonShapeScale;
        if (ImGui.SliderFloat("Shape Scale", ref shapeScale, 0.1f, 3.0f))
        {
            Configuration.SplatoonShapeScale = shapeScale;
            Configuration.Save();
        }

        var fillInside = Configuration.SplatoonShapesFillInside;
        if (ImGui.Checkbox("Fill Inside", ref fillInside))
        {
            Configuration.SplatoonShapesFillInside = fillInside;
            Configuration.Save();
        }

        var opacity = Configuration.SplatoonShapeOpacity;
        if (ImGui.SliderFloat("Shape Opacity", ref opacity, 0.1f, 1.0f))
        {
            Configuration.SplatoonShapeOpacity = opacity;
            Configuration.Save();
        }
    }

    private void DrawTraditionalIndicatorSettings()
    {
        // Show settings based on selected traditional indicator type
        switch (Configuration.IndicatorType)
        {
            case IndicatorType.Lines:
                DrawLineSettings();
                break;
            case IndicatorType.Outlines:
                DrawOutlineSettings();
                break;
            case IndicatorType.Nameplates:
                DrawNameplateSettings();
                break;
            case IndicatorType.Icons:
                DrawIconSettings();
                break;
            case IndicatorType.DirectionalArrows:
                DrawArrowSettings();
                break;
            case IndicatorType.HealthBars:
                DrawHealthBarSettings();
                break;
            case IndicatorType.Combination:
                DrawCombinationSettings();
                break;
            case IndicatorType.ScreenEdgeArrows:
                DrawScreenEdgeArrowSettings();
                break;
            case IndicatorType.GradientLines:
                DrawGradientLineSettings();
                break;
            case IndicatorType.AnimatedPulse:
                DrawAnimatedPulseSettings();
                break;
        }
    }
}
