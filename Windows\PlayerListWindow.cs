using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using Dalamud.Interface.Windowing;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class PlayerListWindow : Window, IDisposable
{
    private readonly Plugin plugin;
    private readonly Configuration config;
    private readonly Dictionary<ulong, float> _lastKnownDistances = new();
    private DateTime _lastUpdate = DateTime.MinValue;
    private const double UpdateIntervalMs = 100; // Update every 100ms for better performance

    public PlayerListWindow(Plugin plugin) : base("Player List###PlayerListWindow", 
        ImGuiWindowFlags.NoScrollbar | ImGuiWindowFlags.AlwaysAutoResize)
    {
        this.plugin = plugin;
        this.config = plugin.Configuration;
        
        SizeConstraints = new WindowSizeConstraints
        {
            MinimumSize = new Vector2(300, 200),
            MaximumSize = new Vector2(600, 800)
        };
    }

    public void Dispose() { }

    public override void Draw()
    {
        if (!config.ShowPlayerList) return;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        // Auto-hide when not in PvP if enabled
        if (config.PlayerListAutoHide && !IsPvPTerritory())
        {
            return;
        }

        var (enemies, allies) = GetNearbyPlayers(localPlayer);



        ImGui.Text($"Players in Range ({enemies.Count + allies.Count})");
        ImGui.Separator();

        if (ImGui.BeginTabBar("PlayerListTabs"))
        {
            if (config.PlayerListShowEnemies && ImGui.BeginTabItem($"Enemies ({enemies.Count})"))
            {
                DrawPlayerTable(enemies, false);
                ImGui.EndTabItem();
            }

            if (config.PlayerListShowAllies && ImGui.BeginTabItem($"Allies ({allies.Count})"))
            {
                DrawPlayerTable(allies, true);
                ImGui.EndTabItem();
            }

            if (ImGui.BeginTabItem("All Players"))
            {
                var allPlayers = new List<IPlayerCharacter>();
                allPlayers.AddRange(enemies);
                allPlayers.AddRange(allies);
                DrawPlayerTable(allPlayers, null);
                ImGui.EndTabItem();
            }

            ImGui.EndTabBar();
        }
    }

    private void DrawPlayerTable(List<IPlayerCharacter> players, bool? isAlly)
    {
        if (players.Count == 0)
        {
            ImGui.Text("No players found");
            return;
        }

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        // Table headers
        var flags = ImGuiTableFlags.Borders | ImGuiTableFlags.RowBg | ImGuiTableFlags.Resizable;
        var columnCount = 2; // Name + Distance (always shown)

        if (config.PlayerListShowJob) columnCount++;
        if (config.PlayerListShowHealth) columnCount++;
        if (config.PlayerListShowStatus) columnCount++;

        if (ImGui.BeginTable("PlayerTable", columnCount, flags))
        {
            // Setup columns
            ImGui.TableSetupColumn("Name", ImGuiTableColumnFlags.WidthFixed, 120);
            ImGui.TableSetupColumn("Distance", ImGuiTableColumnFlags.WidthFixed, 60);
            
            if (config.PlayerListShowJob)
                ImGui.TableSetupColumn("Job", ImGuiTableColumnFlags.WidthFixed, 60);
            if (config.PlayerListShowHealth)
                ImGui.TableSetupColumn("HP", ImGuiTableColumnFlags.WidthFixed, 50);
            if (config.PlayerListShowStatus)
                ImGui.TableSetupColumn("Status", ImGuiTableColumnFlags.WidthStretch);

            ImGui.TableHeadersRow();

            // Sort players by distance
            var sortedPlayers = players.OrderBy(p => Vector3.Distance(localPlayer.Position, p.Position)).ToList();

            foreach (var player in sortedPlayers)
            {
                if (player?.IsValid() != true) continue;

                // Use cached distance if available, otherwise calculate
                var distance = _lastKnownDistances.TryGetValue(player.GameObjectId, out var cachedDistance)
                    ? cachedDistance
                    : Vector3.Distance(localPlayer.Position, player.Position);

                var playerIsAlly = isAlly ?? IsAlly(player);

                ImGui.TableNextRow();

                // Name column
                ImGui.TableNextColumn();

                // Color the name based on ally/enemy status
                var nameColor = playerIsAlly ? new Vector4(0, 1, 0, 1) : new Vector4(1, 0.3f, 0.3f, 1);

                ImGui.TextColored(nameColor, player.Name.TextValue ?? "Unknown");

                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip($"Player: {player.Name.TextValue}");
                }

                // Distance column
                ImGui.TableNextColumn();
                ImGui.Text($"{distance:F1}y");

                // Job column
                if (config.PlayerListShowJob)
                {
                    ImGui.TableNextColumn();
                    var role = JobHelper.GetPlayerRole(player);
                    var roleColor = JobHelper.GetRoleColor(role);
                    ImGui.TextColored(roleColor, role.ToString().Substring(0, Math.Min(4, role.ToString().Length)));
                }

                // Health column
                if (config.PlayerListShowHealth)
                {
                    ImGui.TableNextColumn();
                    var healthPercent = GetHealthPercentage(player);
                    var healthColor = healthPercent < 30 ? new Vector4(1, 0, 0, 1) : 
                                     healthPercent < 60 ? new Vector4(1, 1, 0, 1) : 
                                     new Vector4(0, 1, 0, 1);
                    ImGui.TextColored(healthColor, $"{healthPercent:F0}%");
                }

                // Status column
                if (config.PlayerListShowStatus)
                {
                    ImGui.TableNextColumn();
                    var statusText = GetPlayerStatusText(player);
                    ImGui.Text(statusText);
                }
            }

            ImGui.EndTable();
        }
    }

    private (List<IPlayerCharacter> enemies, List<IPlayerCharacter> allies) GetNearbyPlayers(IPlayerCharacter localPlayer)
    {
        var enemies = new List<IPlayerCharacter>();
        var allies = new List<IPlayerCharacter>();
        var maxDistanceSquared = config.PlayerListMaxDistance * config.PlayerListMaxDistance;
        var localPosition = localPlayer.Position;
        var localGameObjectId = localPlayer.GameObjectId;

        // Pre-allocate with reasonable capacity
        enemies.Capacity = 5;
        allies.Capacity = 5;

        foreach (var obj in Plugin.ObjectTable)
        {
            if (obj is not IPlayerCharacter player ||
                player.GameObjectId == localGameObjectId ||
                !player.IsValid() ||
                player.IsDead)
                continue;

            // Use squared distance for better performance
            var distanceSquared = Vector3.DistanceSquared(localPosition, player.Position);
            if (distanceSquared > maxDistanceSquared)
                continue;

            // Cache the actual distance for UI display
            var distance = (float)Math.Sqrt(distanceSquared);
            _lastKnownDistances[player.GameObjectId] = distance;

            if (IsAlly(player))
                allies.Add(player);
            else
                enemies.Add(player);
        }

        return (enemies, allies);
    }

    private bool IsAlly(IPlayerCharacter player)
    {
        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return false;

        // In PvP, check if they're on the same team
        // This is a simplified check - you might need more sophisticated logic
        return Plugin.PartyList.Any(p => p.GameObject?.GameObjectId == player.GameObjectId) ||
               player.StatusFlags.HasFlag(Dalamud.Game.ClientState.Objects.Enums.StatusFlags.AllianceMember);
    }



    private bool IsPvPTerritory()
    {
        var territoryType = Plugin.ClientState.TerritoryType;
        // Crystalline Conflict territory IDs
        return territoryType is 554 or 1002 or 1007 or 1008 or 1009 or 1155 or 1156 or 1157 or 1158;
    }

    private static float GetHealthPercentage(IPlayerCharacter player)
    {
        if (player.MaxHp == 0) return 100f;
        return (float)player.CurrentHp / player.MaxHp * 100f;
    }

    private static string GetPlayerStatusText(IPlayerCharacter player)
    {
        var status = new List<string>();

        if (StatusEffectHelper.HasImportantStatusEffects(player))
        {
            var effects = StatusEffectHelper.GetImportantStatusEffects(player, true);
            foreach (var effect in effects.Take(2)) // Show max 2 effects
            {
                status.Add(effect.Name);
            }
        }

        if (status.Count == 0)
            return "Normal";

        return string.Join(", ", status);
    }


}
