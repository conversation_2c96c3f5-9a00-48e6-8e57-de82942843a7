{"version": 2, "dgSpecHash": "1EztN6c6jE0=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\GithubProject\\CCDraw - Copy\\PvPLinePlugin\\PvPLinePlugin.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\dalamudpackager\\11.0.0\\dalamudpackager.11.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ecommons\\3.0.0.2\\ecommons.3.0.0.2.nupkg.sha512"], "logs": [{"code": "NU1601", "level": "Warning", "message": "Dependency specified was <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (>= 2.1.14) but ended up with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 11.0.0.", "projectPath": "C:\\Users\\<USER>\\Desktop\\GithubProject\\CCDraw - Copy\\PvPLinePlugin\\PvPLinePlugin.csproj", "warningLevel": 1, "filePath": "C:\\Users\\<USER>\\Desktop\\GithubProject\\CCDraw - Copy\\PvPLinePlugin\\PvPLinePlugin.csproj", "libraryId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetGraphs": ["net9.0-windows7.0"]}]}